import React, { Component, ErrorInfo, ReactNode } from 'react';

// 错误类型定义
export interface AppError {
  id: string;
  message: string;
  stack?: string;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  context?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'network' | 'runtime' | 'ui' | 'business' | 'unknown';
}

// 错误监控类
class ErrorMonitor {
  private static instance: ErrorMonitor;
  private errors: AppError[] = [];
  private maxErrors = 100;
  private listeners: ((error: AppError) => void)[] = [];

  static getInstance(): ErrorMonitor {
    if (!ErrorMonitor.instance) {
      ErrorMonitor.instance = new ErrorMonitor();
    }
    return ErrorMonitor.instance;
  }

  constructor() {
    this.setupGlobalErrorHandlers();
  }

  // 设置全局错误处理器
  private setupGlobalErrorHandlers() {
    // 捕获JavaScript运行时错误
    window.addEventListener('error', (event) => {
      this.captureError({
        message: event.message,
        stack: event.error?.stack,
        category: 'runtime',
        severity: 'high',
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        }
      });
    });

    // 捕获Promise未处理的拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        category: 'runtime',
        severity: 'high',
        context: {
          reason: event.reason,
        }
      });
    });

    // 捕获资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.captureError({
          message: `Resource loading error: ${(event.target as any)?.src || (event.target as any)?.href}`,
          category: 'network',
          severity: 'medium',
          context: {
            element: event.target?.tagName,
            source: (event.target as any)?.src || (event.target as any)?.href,
          }
        });
      }
    }, true);
  }

  // 捕获错误
  captureError(errorInfo: Partial<AppError>) {
    const error: AppError = {
      id: this.generateErrorId(),
      message: errorInfo.message || 'Unknown error',
      stack: errorInfo.stack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.getCurrentUserId(),
      context: errorInfo.context || {},
      severity: errorInfo.severity || 'medium',
      category: errorInfo.category || 'unknown',
    };

    // 添加到错误列表
    this.errors.unshift(error);
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // 通知监听器
    this.listeners.forEach(listener => {
      try {
        listener(error);
      } catch (e) {
        console.error('Error in error listener:', e);
      }
    });

    // 发送到服务器（如果配置了）
    this.sendToServer(error);

    // 开发环境下打印到控制台
    if (process.env.NODE_ENV === 'development') {
      console.error('Captured error:', error);
    }
  }

  // 添加错误监听器
  addListener(listener: (error: AppError) => void) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // 获取错误列表
  getErrors(): AppError[] {
    return [...this.errors];
  }

  // 清除错误
  clearErrors() {
    this.errors = [];
  }

  // 获取错误统计
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      bySeverity: {} as Record<string, number>,
      byCategory: {} as Record<string, number>,
      recent: this.errors.filter(e => Date.now() - e.timestamp < 24 * 60 * 60 * 1000).length,
    };

    this.errors.forEach(error => {
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
      stats.byCategory[error.category] = (stats.byCategory[error.category] || 0) + 1;
    });

    return stats;
  }

  // 生成错误ID
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取当前用户ID（需要根据实际认证系统实现）
  private getCurrentUserId(): string | undefined {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.id;
    } catch {
      return undefined;
    }
  }

  // 发送错误到服务器
  private async sendToServer(error: AppError) {
    try {
      // 这里应该调用实际的错误报告API
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      });
    } catch (e) {
      // 静默失败，避免错误报告本身引起错误
      console.warn('Failed to send error to server:', e);
    }
  }
}

// React错误边界组件
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: ErrorInfo) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // 报告错误
    errorMonitor.captureError({
      message: error.message,
      stack: error.stack,
      category: 'ui',
      severity: 'high',
      context: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
      }
    });

    // 调用自定义错误处理器
    this.props.onError?.(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback && this.state.error && this.state.errorInfo) {
        return this.props.fallback(this.state.error, this.state.errorInfo);
      }

      return (
        <div className="error-boundary">
          <div className="error-boundary-content">
            <h2>出现了一些问题</h2>
            <p>页面遇到了错误，请刷新页面重试。</p>
            <details style={{ whiteSpace: 'pre-wrap' }}>
              <summary>错误详情</summary>
              {this.state.error && this.state.error.toString()}
              <br />
              {this.state.errorInfo?.componentStack}
            </details>
            <button
              onClick={() => window.location.reload()}
              className="btn btn-primary"
            >
              刷新页面
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 错误处理Hook
export function useErrorHandler() {
  const [errors, setErrors] = React.useState<AppError[]>([]);

  React.useEffect(() => {
    const unsubscribe = errorMonitor.addListener((error) => {
      setErrors(prev => [error, ...prev.slice(0, 9)]); // 只保留最近10个错误
    });

    return unsubscribe;
  }, []);

  const captureError = React.useCallback((errorInfo: Partial<AppError>) => {
    errorMonitor.captureError(errorInfo);
  }, []);

  const clearErrors = React.useCallback(() => {
    setErrors([]);
  }, []);

  return {
    errors,
    captureError,
    clearErrors,
    errorStats: errorMonitor.getErrorStats(),
  };
}

// 网络错误处理
export function handleNetworkError(error: any, context?: Record<string, any>) {
  let message = 'Network error occurred';
  let severity: AppError['severity'] = 'medium';

  if (error.response) {
    // 服务器响应错误
    message = `HTTP ${error.response.status}: ${error.response.statusText}`;
    severity = error.response.status >= 500 ? 'high' : 'medium';
  } else if (error.request) {
    // 请求发送失败
    message = 'Network request failed';
    severity = 'high';
  } else {
    // 其他错误
    message = error.message || 'Unknown network error';
  }

  errorMonitor.captureError({
    message,
    stack: error.stack,
    category: 'network',
    severity,
    context: {
      ...context,
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
    }
  });
}

// 业务逻辑错误处理
export function handleBusinessError(message: string, context?: Record<string, any>) {
  errorMonitor.captureError({
    message,
    category: 'business',
    severity: 'medium',
    context,
  });
}

// 用户操作错误处理
export function handleUserError(message: string, context?: Record<string, any>) {
  errorMonitor.captureError({
    message,
    category: 'ui',
    severity: 'low',
    context,
  });
}

// 重试机制
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: {
    maxAttempts?: number;
    delay?: number;
    backoff?: boolean;
    onRetry?: (attempt: number, error: any) => void;
  } = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    delay = 1000,
    backoff = true,
    onRetry
  } = options;

  let lastError: any;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts) {
        break;
      }

      onRetry?.(attempt, error);

      const waitTime = backoff ? delay * Math.pow(2, attempt - 1) : delay;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  throw lastError;
}

// 安全执行函数（捕获错误但不抛出）
export function safeExecute<T>(
  fn: () => T,
  fallback?: T,
  onError?: (error: any) => void
): T | undefined {
  try {
    return fn();
  } catch (error) {
    onError?.(error);
    errorMonitor.captureError({
      message: `Safe execution failed: ${error}`,
      stack: error instanceof Error ? error.stack : undefined,
      category: 'runtime',
      severity: 'low',
    });
    return fallback;
  }
}

// 创建错误监控实例
export const errorMonitor = ErrorMonitor.getInstance();

// 默认导出错误边界组件
export default ErrorBoundary;
