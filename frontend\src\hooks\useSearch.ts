import { useState, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import searchService, { SearchParams, GlobalSearchParams } from '@/services/searchService';
import toast from 'react-hot-toast';

export interface UseSearchOptions {
  initialParams?: SearchParams;
  enabled?: boolean;
  autoSearch?: boolean;
}

export const useSearch = (options: UseSearchOptions = {}) => {
  const { initialParams = {}, enabled = true, autoSearch = true } = options;
  const [params, setParams] = useState<SearchParams>({
    page: 1,
    limit: 20,
    sortBy: 'relevance',
    ...initialParams
  });

  const queryClient = useQueryClient();

  // 产品搜索
  const {
    data: searchData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['search-products', params],
    queryFn: () => searchService.searchProducts(params),
    enabled: enabled && autoSearch && !!params.query,
    staleTime: 30 * 1000, // 30秒
    cacheTime: 5 * 60 * 1000, // 5分钟
  });

  // 获取搜索建议
  const {
    data: suggestions,
    isLoading: isLoadingSuggestions
  } = useQuery({
    queryKey: ['search-suggestions', params.query],
    queryFn: () => searchService.getSearchSuggestions(params.query || '', 'product'),
    enabled: enabled && !!params.query && params.query.length > 1,
    staleTime: 60 * 1000, // 1分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
  });

  // 获取热门搜索
  const {
    data: popularSearches,
    isLoading: isLoadingPopular
  } = useQuery({
    queryKey: ['popular-searches'],
    queryFn: () => searchService.getPopularSearches(),
    staleTime: 10 * 60 * 1000, // 10分钟
    cacheTime: 30 * 60 * 1000, // 30分钟
  });

  // 获取搜索历史
  const {
    data: searchHistory,
    isLoading: isLoadingHistory,
    refetch: refetchHistory
  } = useQuery({
    queryKey: ['search-history'],
    queryFn: () => searchService.getSearchHistory(),
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
  });

  // 获取过滤器选项
  const {
    data: filterOptions,
    isLoading: isLoadingFilters
  } = useQuery({
    queryKey: ['search-filters'],
    queryFn: () => searchService.getFilterOptions(),
    staleTime: 30 * 60 * 1000, // 30分钟
    cacheTime: 60 * 60 * 1000, // 1小时
  });

  // 记录搜索
  const recordSearchMutation = useMutation({
    mutationFn: ({ query, type }: { query: string; type?: string }) =>
      searchService.recordSearch(query, type),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['search-history'] });
      queryClient.invalidateQueries({ queryKey: ['popular-searches'] });
    }
  });

  // 清除搜索历史
  const clearHistoryMutation = useMutation({
    mutationFn: () => searchService.clearSearchHistory(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['search-history'] });
      toast.success('搜索历史已清除');
    },
    onError: (error: any) => {
      toast.error(error.message || '清除搜索历史失败');
    }
  });

  // 更新搜索参数
  const updateParams = useCallback((newParams: Partial<SearchParams>) => {
    setParams(prev => ({
      ...prev,
      ...newParams,
      page: newParams.page || 1 // 重置页码除非明确指定
    }));
  }, []);

  // 执行搜索
  const search = useCallback((query: string, additionalParams: Partial<SearchParams> = {}) => {
    const searchParams = {
      ...additionalParams,
      query,
      page: 1
    };
    
    setParams(prev => ({
      ...prev,
      ...searchParams
    }));

    // 记录搜索
    if (query.trim()) {
      recordSearchMutation.mutate({ query: query.trim(), type: 'product' });
    }
  }, [recordSearchMutation]);

  // 清空搜索
  const clearSearch = useCallback(() => {
    setParams(prev => ({
      ...prev,
      query: undefined,
      page: 1
    }));
  }, []);

  // 应用过滤器
  const applyFilters = useCallback((filters: Partial<SearchParams>) => {
    updateParams({ ...filters, page: 1 });
  }, [updateParams]);

  // 排序
  const sortBy = useCallback((sortBy: SearchParams['sortBy']) => {
    updateParams({ sortBy, page: 1 });
  }, [updateParams]);

  // 分页
  const goToPage = useCallback((page: number) => {
    updateParams({ page });
  }, [updateParams]);

  // 刷新搜索
  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    // 数据
    results: searchData?.results || [],
    pagination: searchData?.pagination,
    facets: searchData?.facets,
    suggestions: suggestions || [],
    popularSearches: popularSearches || [],
    searchHistory: searchHistory || [],
    filterOptions,
    
    // 状态
    isLoading,
    isLoadingSuggestions,
    isLoadingPopular,
    isLoadingHistory,
    isLoadingFilters,
    error,
    
    // 搜索参数
    params,
    updateParams,
    
    // 操作方法
    search,
    clearSearch,
    applyFilters,
    sortBy,
    goToPage,
    refresh,
    clearHistory: clearHistoryMutation.mutate,
    
    // 状态
    isRecording: recordSearchMutation.isPending,
    isClearingHistory: clearHistoryMutation.isPending,
  };
};

// 全局搜索Hook
export const useGlobalSearch = () => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const {
    data: searchData,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['global-search', query],
    queryFn: () => searchService.globalSearch({ query, limit: 10 }),
    enabled: !!query && query.length > 1,
    staleTime: 30 * 1000,
    cacheTime: 2 * 60 * 1000,
  });

  const search = useCallback((searchQuery: string) => {
    setQuery(searchQuery);
    if (searchQuery.trim()) {
      searchService.recordSearch(searchQuery.trim(), 'global');
    }
  }, []);

  const openSearch = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeSearch = useCallback(() => {
    setIsOpen(false);
    setQuery('');
  }, []);

  return {
    // 数据
    results: searchData,
    query,
    isOpen,
    
    // 状态
    isLoading,
    
    // 操作方法
    search,
    openSearch,
    closeSearch,
    setQuery,
    refetch
  };
};

// 相似产品搜索Hook
export const useSimilarProducts = (productId: string, enabled: boolean = true) => {
  const {
    data: similarProducts,
    isLoading,
    error
  } = useQuery({
    queryKey: ['similar-products', productId],
    queryFn: () => searchService.findSimilarProducts(productId),
    enabled: enabled && !!productId,
    staleTime: 10 * 60 * 1000, // 10分钟
    cacheTime: 30 * 60 * 1000, // 30分钟
  });

  return {
    similarProducts: similarProducts || [],
    isLoading,
    error
  };
};

// 高级搜索Hook
export const useAdvancedSearch = () => {
  const [filters, setFilters] = useState<any>({});
  const [isOpen, setIsOpen] = useState(false);

  const {
    data: searchData,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ['advanced-search', filters],
    queryFn: () => searchService.advancedSearch(filters),
    enabled: Object.keys(filters).length > 0,
    staleTime: 30 * 1000,
    cacheTime: 5 * 60 * 1000,
  });

  const applyFilters = useCallback((newFilters: any) => {
    setFilters(newFilters);
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  const openAdvancedSearch = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeAdvancedSearch = useCallback(() => {
    setIsOpen(false);
  }, []);

  return {
    // 数据
    results: searchData?.results || [],
    pagination: searchData?.pagination,
    facets: searchData?.facets,
    filters,
    isOpen,
    
    // 状态
    isLoading,
    
    // 操作方法
    applyFilters,
    clearFilters,
    openAdvancedSearch,
    closeAdvancedSearch,
    refetch
  };
};

// URL搜索参数同步Hook
export const useSearchUrl = () => {
  const router = useRouter();

  const updateUrl = useCallback((params: SearchParams) => {
    const url = searchService.buildSearchUrl(params);
    router.push(url, undefined, { shallow: true });
  }, [router]);

  const parseUrl = useCallback(() => {
    return searchService.parseSearchUrl(router.asPath);
  }, [router.asPath]);

  return {
    updateUrl,
    parseUrl
  };
};
