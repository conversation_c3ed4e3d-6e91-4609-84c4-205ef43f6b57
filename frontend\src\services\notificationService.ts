import { EventEmitter } from 'events';
import { getWebSocketService } from './websocketService';

export interface NotificationData {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  actions?: Array<{
    label: string;
    action: string;
    style?: 'primary' | 'secondary' | 'danger';
  }>;
  metadata?: Record<string, any>;
  category?: 'system' | 'task' | 'user' | 'security';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  expiresAt?: number;
}

export interface NotificationSettings {
  enabled: boolean;
  categories: {
    system: boolean;
    task: boolean;
    user: boolean;
    security: boolean;
  };
  sound: boolean;
  desktop: boolean;
  email: boolean;
  doNotDisturb: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}

class NotificationService extends EventEmitter {
  private static instance: NotificationService;
  private notifications: NotificationData[] = [];
  private settings: NotificationSettings;
  private maxNotifications = 100;
  private soundEnabled = true;
  private desktopPermission: NotificationPermission = 'default';

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  constructor() {
    super();
    this.settings = this.loadSettings();
    this.initializeDesktopNotifications();
    this.setupWebSocketListener();
  }

  // 初始化桌面通知
  private async initializeDesktopNotifications() {
    if ('Notification' in window) {
      this.desktopPermission = Notification.permission;
      
      if (this.desktopPermission === 'default') {
        this.desktopPermission = await Notification.requestPermission();
      }
    }
  }

  // 设置WebSocket监听器
  private setupWebSocketListener() {
    const ws = getWebSocketService();
    if (ws) {
      ws.on('message:notification', (data: NotificationData) => {
        this.addNotification(data);
      });
    }
  }

  // 加载设置
  private loadSettings(): NotificationSettings {
    try {
      const saved = localStorage.getItem('notificationSettings');
      if (saved) {
        return { ...this.getDefaultSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
    }
    return this.getDefaultSettings();
  }

  // 获取默认设置
  private getDefaultSettings(): NotificationSettings {
    return {
      enabled: true,
      categories: {
        system: true,
        task: true,
        user: true,
        security: true,
      },
      sound: true,
      desktop: true,
      email: false,
      doNotDisturb: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
      },
    };
  }

  // 保存设置
  private saveSettings() {
    try {
      localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    } catch (error) {
      console.error('Failed to save notification settings:', error);
    }
  }

  // 添加通知
  addNotification(notification: Partial<NotificationData>): string {
    const fullNotification: NotificationData = {
      id: notification.id || this.generateId(),
      type: notification.type || 'info',
      title: notification.title || '通知',
      message: notification.message || '',
      timestamp: notification.timestamp || Date.now(),
      read: notification.read || false,
      actions: notification.actions,
      metadata: notification.metadata,
      category: notification.category || 'system',
      priority: notification.priority || 'normal',
      expiresAt: notification.expiresAt,
    };

    // 检查是否应该显示通知
    if (!this.shouldShowNotification(fullNotification)) {
      return fullNotification.id;
    }

    // 添加到列表
    this.notifications.unshift(fullNotification);
    
    // 限制通知数量
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.maxNotifications);
    }

    // 发出事件
    this.emit('notification', fullNotification);
    this.emit('notificationAdded', fullNotification);

    // 显示桌面通知
    if (this.settings.desktop && this.desktopPermission === 'granted') {
      this.showDesktopNotification(fullNotification);
    }

    // 播放声音
    if (this.settings.sound && this.soundEnabled) {
      this.playNotificationSound(fullNotification.type);
    }

    return fullNotification.id;
  }

  // 检查是否应该显示通知
  private shouldShowNotification(notification: NotificationData): boolean {
    // 检查总开关
    if (!this.settings.enabled) {
      return false;
    }

    // 检查分类开关
    if (!this.settings.categories[notification.category || 'system']) {
      return false;
    }

    // 检查免打扰模式
    if (this.settings.doNotDisturb.enabled && this.isInDoNotDisturbTime()) {
      // 紧急通知仍然显示
      if (notification.priority !== 'urgent') {
        return false;
      }
    }

    // 检查过期时间
    if (notification.expiresAt && Date.now() > notification.expiresAt) {
      return false;
    }

    return true;
  }

  // 检查是否在免打扰时间
  private isInDoNotDisturbTime(): boolean {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = this.settings.doNotDisturb.startTime.split(':').map(Number);
    const [endHour, endMin] = this.settings.doNotDisturb.endTime.split(':').map(Number);
    
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // 跨天的情况
      return currentTime >= startTime || currentTime <= endTime;
    }
  }

  // 显示桌面通知
  private showDesktopNotification(notification: NotificationData) {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
      return;
    }

    const desktopNotification = new Notification(notification.title, {
      body: notification.message,
      icon: this.getNotificationIcon(notification.type),
      tag: notification.id,
      requireInteraction: notification.priority === 'urgent',
    });

    desktopNotification.onclick = () => {
      window.focus();
      this.markAsRead(notification.id);
      this.emit('notificationClick', notification);
      desktopNotification.close();
    };

    // 自动关闭
    setTimeout(() => {
      desktopNotification.close();
    }, 5000);
  }

  // 播放通知声音
  private playNotificationSound(type: NotificationData['type']) {
    try {
      const audio = new Audio();
      switch (type) {
        case 'success':
          audio.src = '/sounds/success.mp3';
          break;
        case 'warning':
          audio.src = '/sounds/warning.mp3';
          break;
        case 'error':
          audio.src = '/sounds/error.mp3';
          break;
        default:
          audio.src = '/sounds/notification.mp3';
      }
      audio.volume = 0.3;
      audio.play().catch(() => {
        // 静默失败，某些浏览器可能阻止自动播放
      });
    } catch (error) {
      console.warn('Failed to play notification sound:', error);
    }
  }

  // 获取通知图标
  private getNotificationIcon(type: NotificationData['type']): string {
    const icons = {
      info: '/icons/info.png',
      success: '/icons/success.png',
      warning: '/icons/warning.png',
      error: '/icons/error.png',
    };
    return icons[type] || icons.info;
  }

  // 标记为已读
  markAsRead(id: string): boolean {
    const notification = this.notifications.find(n => n.id === id);
    if (notification && !notification.read) {
      notification.read = true;
      this.emit('notificationRead', notification);
      return true;
    }
    return false;
  }

  // 标记所有为已读
  markAllAsRead(): number {
    let count = 0;
    this.notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
        count++;
      }
    });
    
    if (count > 0) {
      this.emit('allNotificationsRead', count);
    }
    
    return count;
  }

  // 删除通知
  removeNotification(id: string): boolean {
    const index = this.notifications.findIndex(n => n.id === id);
    if (index > -1) {
      const notification = this.notifications.splice(index, 1)[0];
      this.emit('notificationRemoved', notification);
      return true;
    }
    return false;
  }

  // 清除所有通知
  clearAll(): number {
    const count = this.notifications.length;
    this.notifications = [];
    this.emit('allNotificationsCleared', count);
    return count;
  }

  // 清除已读通知
  clearRead(): number {
    const readNotifications = this.notifications.filter(n => n.read);
    this.notifications = this.notifications.filter(n => !n.read);
    this.emit('readNotificationsCleared', readNotifications.length);
    return readNotifications.length;
  }

  // 获取通知列表
  getNotifications(options: {
    unreadOnly?: boolean;
    category?: string;
    limit?: number;
  } = {}): NotificationData[] {
    let filtered = [...this.notifications];

    if (options.unreadOnly) {
      filtered = filtered.filter(n => !n.read);
    }

    if (options.category) {
      filtered = filtered.filter(n => n.category === options.category);
    }

    if (options.limit) {
      filtered = filtered.slice(0, options.limit);
    }

    return filtered;
  }

  // 获取未读数量
  getUnreadCount(category?: string): number {
    let notifications = this.notifications.filter(n => !n.read);
    
    if (category) {
      notifications = notifications.filter(n => n.category === category);
    }
    
    return notifications.length;
  }

  // 获取设置
  getSettings(): NotificationSettings {
    return { ...this.settings };
  }

  // 更新设置
  updateSettings(newSettings: Partial<NotificationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
    this.emit('settingsUpdated', this.settings);
  }

  // 执行通知操作
  executeAction(notificationId: string, actionName: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      this.emit('notificationAction', { notification, action: actionName });
    }
  }

  // 生成ID
  private generateId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 清理过期通知
  cleanupExpired(): number {
    const now = Date.now();
    const beforeCount = this.notifications.length;
    
    this.notifications = this.notifications.filter(n => 
      !n.expiresAt || n.expiresAt > now
    );
    
    const removedCount = beforeCount - this.notifications.length;
    
    if (removedCount > 0) {
      this.emit('expiredNotificationsCleared', removedCount);
    }
    
    return removedCount;
  }
}

// React Hook
import React from 'react';

export function useNotifications() {
  const [notifications, setNotifications] = React.useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = React.useState(0);
  const [settings, setSettings] = React.useState<NotificationSettings>();

  React.useEffect(() => {
    const service = NotificationService.getInstance();
    
    // 初始化数据
    setNotifications(service.getNotifications());
    setUnreadCount(service.getUnreadCount());
    setSettings(service.getSettings());

    // 监听事件
    const handleNotificationAdded = (notification: NotificationData) => {
      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
    };

    const handleNotificationRead = () => {
      setNotifications(service.getNotifications());
      setUnreadCount(service.getUnreadCount());
    };

    const handleNotificationRemoved = () => {
      setNotifications(service.getNotifications());
      setUnreadCount(service.getUnreadCount());
    };

    const handleSettingsUpdated = (newSettings: NotificationSettings) => {
      setSettings(newSettings);
    };

    service.on('notificationAdded', handleNotificationAdded);
    service.on('notificationRead', handleNotificationRead);
    service.on('allNotificationsRead', handleNotificationRead);
    service.on('notificationRemoved', handleNotificationRemoved);
    service.on('allNotificationsCleared', handleNotificationRemoved);
    service.on('settingsUpdated', handleSettingsUpdated);

    return () => {
      service.off('notificationAdded', handleNotificationAdded);
      service.off('notificationRead', handleNotificationRead);
      service.off('allNotificationsRead', handleNotificationRead);
      service.off('notificationRemoved', handleNotificationRemoved);
      service.off('allNotificationsCleared', handleNotificationRemoved);
      service.off('settingsUpdated', handleSettingsUpdated);
    };
  }, []);

  const addNotification = React.useCallback((notification: Partial<NotificationData>) => {
    return NotificationService.getInstance().addNotification(notification);
  }, []);

  const markAsRead = React.useCallback((id: string) => {
    return NotificationService.getInstance().markAsRead(id);
  }, []);

  const markAllAsRead = React.useCallback(() => {
    return NotificationService.getInstance().markAllAsRead();
  }, []);

  const removeNotification = React.useCallback((id: string) => {
    return NotificationService.getInstance().removeNotification(id);
  }, []);

  const clearAll = React.useCallback(() => {
    return NotificationService.getInstance().clearAll();
  }, []);

  const updateSettings = React.useCallback((newSettings: Partial<NotificationSettings>) => {
    NotificationService.getInstance().updateSettings(newSettings);
  }, []);

  return {
    notifications,
    unreadCount,
    settings,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    updateSettings,
  };
}

// 创建通知服务实例
export const notificationService = NotificationService.getInstance();

export default NotificationService;
