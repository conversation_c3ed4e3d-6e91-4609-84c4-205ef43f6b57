import apiService from './api';
import { Product, AnalysisTask } from '@/types';

export interface SearchParams {
  query?: string;
  category?: string;
  platform?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'rating' | 'sales' | 'created_at';
  page?: number;
  limit?: number;
}

export interface SearchResult {
  id: string;
  type: 'product' | 'analysis' | 'import_task';
  title: string;
  description?: string;
  url?: string;
  imageUrl?: string;
  price?: number;
  rating?: number;
  category?: string;
  platform?: string;
  brand?: string;
  tags?: string[];
  createdAt: string;
  relevanceScore: number;
}

export interface SearchResponse {
  results: SearchResult[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  facets: {
    categories: Array<{ name: string; count: number }>;
    platforms: Array<{ name: string; count: number }>;
    brands: Array<{ name: string; count: number }>;
    priceRanges: Array<{ range: string; count: number }>;
  };
  suggestions: string[];
}

export interface GlobalSearchParams {
  query: string;
  types?: Array<'product' | 'analysis' | 'import_task'>;
  limit?: number;
}

export interface GlobalSearchResponse {
  products: SearchResult[];
  analyses: SearchResult[];
  importTasks: SearchResult[];
  total: number;
}

class SearchService {
  private baseUrl = '/search';

  /**
   * 全局搜索
   */
  async globalSearch(params: GlobalSearchParams): Promise<GlobalSearchResponse> {
    try {
      const response = await apiService.get<{ data: GlobalSearchResponse }>(
        `${this.baseUrl}/global`,
        params
      );
      return response.data;
    } catch (error) {
      console.error('全局搜索失败:', error);
      throw error;
    }
  }

  /**
   * 产品搜索
   */
  async searchProducts(params: SearchParams): Promise<SearchResponse> {
    try {
      const response = await apiService.get<{ data: SearchResponse }>(
        `${this.baseUrl}/products`,
        params
      );
      return response.data;
    } catch (error) {
      console.error('产品搜索失败:', error);
      throw error;
    }
  }

  /**
   * 分析任务搜索
   */
  async searchAnalyses(params: Omit<SearchParams, 'platform' | 'minPrice' | 'maxPrice'>): Promise<SearchResponse> {
    try {
      const response = await apiService.get<{ data: SearchResponse }>(
        `${this.baseUrl}/analyses`,
        params
      );
      return response.data;
    } catch (error) {
      console.error('分析搜索失败:', error);
      throw error;
    }
  }

  /**
   * 获取搜索建议
   */
  async getSearchSuggestions(query: string, type?: 'product' | 'analysis'): Promise<string[]> {
    try {
      const response = await apiService.get<{ data: { suggestions: string[] } }>(
        `${this.baseUrl}/suggestions`,
        { query, type }
      );
      return response.data.suggestions;
    } catch (error) {
      console.error('获取搜索建议失败:', error);
      return [];
    }
  }

  /**
   * 获取热门搜索词
   */
  async getPopularSearches(limit: number = 10): Promise<Array<{ query: string; count: number }>> {
    try {
      const response = await apiService.get<{ 
        data: { searches: Array<{ query: string; count: number }> } 
      }>(`${this.baseUrl}/popular`, { limit });
      return response.data.searches;
    } catch (error) {
      console.error('获取热门搜索失败:', error);
      return [];
    }
  }

  /**
   * 记录搜索历史
   */
  async recordSearch(query: string, type?: string): Promise<void> {
    try {
      await apiService.post(`${this.baseUrl}/history`, { query, type });
    } catch (error) {
      console.error('记录搜索历史失败:', error);
      // 不抛出错误，因为这不是关键功能
    }
  }

  /**
   * 获取搜索历史
   */
  async getSearchHistory(limit: number = 20): Promise<Array<{ query: string; searchedAt: string }>> {
    try {
      const response = await apiService.get<{ 
        data: { history: Array<{ query: string; searchedAt: string }> } 
      }>(`${this.baseUrl}/history`, { limit });
      return response.data.history;
    } catch (error) {
      console.error('获取搜索历史失败:', error);
      return [];
    }
  }

  /**
   * 清除搜索历史
   */
  async clearSearchHistory(): Promise<void> {
    try {
      await apiService.delete(`${this.baseUrl}/history`);
    } catch (error) {
      console.error('清除搜索历史失败:', error);
      throw error;
    }
  }

  /**
   * 高级搜索
   */
  async advancedSearch(params: {
    query?: string;
    filters: {
      categories?: string[];
      platforms?: string[];
      brands?: string[];
      priceRange?: { min?: number; max?: number };
      ratingRange?: { min?: number; max?: number };
      dateRange?: { start?: string; end?: string };
      tags?: string[];
    };
    sortBy?: string;
    page?: number;
    limit?: number;
  }): Promise<SearchResponse> {
    try {
      const response = await apiService.post<{ data: SearchResponse }>(
        `${this.baseUrl}/advanced`,
        params
      );
      return response.data;
    } catch (error) {
      console.error('高级搜索失败:', error);
      throw error;
    }
  }

  /**
   * 相似产品搜索
   */
  async findSimilarProducts(productId: string, limit: number = 10): Promise<SearchResult[]> {
    try {
      const response = await apiService.get<{ data: { products: SearchResult[] } }>(
        `${this.baseUrl}/similar/${productId}`,
        { limit }
      );
      return response.data.products;
    } catch (error) {
      console.error('查找相似产品失败:', error);
      throw error;
    }
  }

  /**
   * 搜索过滤器选项
   */
  async getFilterOptions(): Promise<{
    categories: string[];
    platforms: string[];
    brands: string[];
    priceRanges: Array<{ label: string; min: number; max: number }>;
  }> {
    try {
      const response = await apiService.get<{ 
        data: {
          categories: string[];
          platforms: string[];
          brands: string[];
          priceRanges: Array<{ label: string; min: number; max: number }>;
        }
      }>(`${this.baseUrl}/filters`);
      return response.data;
    } catch (error) {
      console.error('获取过滤器选项失败:', error);
      return {
        categories: [],
        platforms: [],
        brands: [],
        priceRanges: []
      };
    }
  }

  /**
   * 格式化搜索结果
   */
  formatSearchResult(result: SearchResult): {
    title: string;
    subtitle: string;
    description: string;
    badge?: string;
    badgeColor?: string;
  } {
    switch (result.type) {
      case 'product':
        return {
          title: result.title,
          subtitle: `${result.brand || ''} · ${result.category || ''}`,
          description: result.description || '',
          badge: result.price ? `¥${result.price.toLocaleString()}` : undefined,
          badgeColor: 'primary'
        };
      case 'analysis':
        return {
          title: result.title,
          subtitle: `分析任务 · ${result.category || ''}`,
          description: result.description || '',
          badge: '分析',
          badgeColor: 'info'
        };
      case 'import_task':
        return {
          title: result.title,
          subtitle: '导入任务',
          description: result.description || '',
          badge: '导入',
          badgeColor: 'warning'
        };
      default:
        return {
          title: result.title,
          subtitle: '',
          description: result.description || ''
        };
    }
  }

  /**
   * 高亮搜索关键词
   */
  highlightSearchTerm(text: string, searchTerm: string): string {
    if (!searchTerm || !text) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  /**
   * 构建搜索URL
   */
  buildSearchUrl(params: SearchParams): string {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });
    
    return `/search?${searchParams.toString()}`;
  }

  /**
   * 解析搜索URL参数
   */
  parseSearchUrl(url: string): SearchParams {
    const urlObj = new URL(url, window.location.origin);
    const params: SearchParams = {};
    
    const query = urlObj.searchParams.get('query') || urlObj.searchParams.get('q');
    if (query) params.query = query;
    
    const category = urlObj.searchParams.get('category');
    if (category) params.category = category;
    
    const platform = urlObj.searchParams.get('platform');
    if (platform) params.platform = platform;
    
    const minPrice = urlObj.searchParams.get('minPrice');
    if (minPrice) params.minPrice = parseFloat(minPrice);
    
    const maxPrice = urlObj.searchParams.get('maxPrice');
    if (maxPrice) params.maxPrice = parseFloat(maxPrice);
    
    const sortBy = urlObj.searchParams.get('sortBy');
    if (sortBy) params.sortBy = sortBy as any;
    
    const page = urlObj.searchParams.get('page');
    if (page) params.page = parseInt(page);
    
    const limit = urlObj.searchParams.get('limit');
    if (limit) params.limit = parseInt(limit);
    
    return params;
  }
}

// 创建搜索服务实例
const searchService = new SearchService();

export default searchService;
