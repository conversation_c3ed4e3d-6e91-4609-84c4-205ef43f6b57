import { EventEmitter } from 'events';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  id?: string;
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  debug?: boolean;
}

export type WebSocketEventType = 
  | 'connected' 
  | 'disconnected' 
  | 'error' 
  | 'message' 
  | 'reconnecting' 
  | 'reconnected'
  | 'heartbeat';

class WebSocketService extends EventEmitter {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isManualClose = false;
  private messageQueue: WebSocketMessage[] = [];

  constructor(config: WebSocketConfig) {
    super();
    this.config = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      debug: false,
      ...config
    };
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        this.once('connected', resolve);
        this.once('error', reject);
        return;
      }

      this.isConnecting = true;
      this.isManualClose = false;

      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols);
        
        this.ws.onopen = () => {
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.processMessageQueue();
          
          this.log('WebSocket connected');
          this.emit('connected');
          resolve();
        };

        this.ws.onclose = (event) => {
          this.isConnecting = false;
          this.stopHeartbeat();
          
          this.log('WebSocket disconnected', event.code, event.reason);
          this.emit('disconnected', { code: event.code, reason: event.reason });

          if (!this.isManualClose && this.shouldReconnect()) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          this.isConnecting = false;
          this.log('WebSocket error', error);
          this.emit('error', error);
          reject(error);
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            this.log('Failed to parse message', event.data);
            this.emit('error', new Error('Invalid message format'));
          }
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.isManualClose = true;
    this.clearReconnectTimer();
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  /**
   * 发送消息
   */
  send(message: Omit<WebSocketMessage, 'timestamp' | 'id'>): boolean {
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: Date.now(),
      id: this.generateMessageId()
    };

    if (this.ws?.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(fullMessage));
        this.log('Message sent', fullMessage);
        return true;
      } catch (error) {
        this.log('Failed to send message', error);
        this.queueMessage(fullMessage);
        return false;
      }
    } else {
      this.queueMessage(fullMessage);
      return false;
    }
  }

  /**
   * 获取连接状态
   */
  getReadyState(): number {
    return this.ws?.readyState ?? WebSocket.CLOSED;
  }

  /**
   * 是否已连接
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    this.log('Message received', message);
    
    // 处理心跳响应
    if (message.type === 'pong') {
      this.emit('heartbeat', message);
      return;
    }

    // 发出消息事件
    this.emit('message', message);
    this.emit(`message:${message.type}`, message.data);
  }

  /**
   * 队列消息（连接断开时）
   */
  private queueMessage(message: WebSocketMessage): void {
    this.messageQueue.push(message);
    
    // 限制队列大小
    if (this.messageQueue.length > 100) {
      this.messageQueue.shift();
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift();
      if (message) {
        this.ws!.send(JSON.stringify(message));
      }
    }
  }

  /**
   * 是否应该重连
   */
  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.config.maxReconnectAttempts!;
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) return;

    this.reconnectAttempts++;
    const delay = this.config.reconnectInterval! * Math.pow(2, this.reconnectAttempts - 1);
    
    this.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    this.emit('reconnecting', { attempt: this.reconnectAttempts, delay });

    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.connect().then(() => {
        this.emit('reconnected');
      }).catch(() => {
        if (this.shouldReconnect()) {
          this.scheduleReconnect();
        }
      });
    }, delay);
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    if (!this.config.heartbeatInterval) return;

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.send({ type: 'ping', data: {} });
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 日志输出
   */
  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[WebSocket]', ...args);
    }
  }
}

// 创建全局WebSocket实例
let globalWebSocket: WebSocketService | null = null;

export const createWebSocketService = (config: WebSocketConfig): WebSocketService => {
  if (globalWebSocket) {
    globalWebSocket.disconnect();
  }
  
  globalWebSocket = new WebSocketService(config);
  return globalWebSocket;
};

export const getWebSocketService = (): WebSocketService | null => {
  return globalWebSocket;
};

// WebSocket Hook
import React from 'react';

export const useWebSocket = (config?: Partial<WebSocketConfig>) => {
  const [isConnected, setIsConnected] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = React.useState(0);
  
  const wsRef = React.useRef<WebSocketService | null>(null);

  React.useEffect(() => {
    if (!config?.url) return;

    const ws = createWebSocketService({
      url: config.url,
      debug: process.env.NODE_ENV === 'development',
      ...config
    });

    wsRef.current = ws;

    const handleConnected = () => {
      setIsConnected(true);
      setError(null);
      setReconnectAttempts(0);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleError = (error: Error) => {
      setError(error);
    };

    const handleReconnecting = ({ attempt }: { attempt: number }) => {
      setReconnectAttempts(attempt);
    };

    ws.on('connected', handleConnected);
    ws.on('disconnected', handleDisconnected);
    ws.on('error', handleError);
    ws.on('reconnecting', handleReconnecting);

    // 自动连接
    ws.connect().catch(console.error);

    return () => {
      ws.off('connected', handleConnected);
      ws.off('disconnected', handleDisconnected);
      ws.off('error', handleError);
      ws.off('reconnecting', handleReconnecting);
      ws.disconnect();
    };
  }, [config?.url]);

  const send = React.useCallback((message: Omit<WebSocketMessage, 'timestamp' | 'id'>) => {
    return wsRef.current?.send(message) ?? false;
  }, []);

  const subscribe = React.useCallback((eventType: string, handler: (data: any) => void) => {
    if (!wsRef.current) return () => {};

    wsRef.current.on(`message:${eventType}`, handler);
    return () => {
      wsRef.current?.off(`message:${eventType}`, handler);
    };
  }, []);

  return {
    isConnected,
    error,
    reconnectAttempts,
    send,
    subscribe,
    webSocket: wsRef.current
  };
};

export default WebSocketService;
