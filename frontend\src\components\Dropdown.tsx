import React, { Fragment, useState, useRef, useEffect } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline';

export interface DropdownItem {
  key: string;
  label: string;
  value?: any;
  icon?: React.ReactNode;
  disabled?: boolean;
  divider?: boolean;
  danger?: boolean;
  onClick?: () => void;
}

export interface DropdownProps {
  items: DropdownItem[];
  trigger?: React.ReactNode;
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
  className?: string;
  disabled?: boolean;
  onSelect?: (item: DropdownItem) => void;
}

const Dropdown: React.FC<DropdownProps> = ({
  items,
  trigger,
  placement = 'bottom-start',
  className = '',
  disabled = false,
  onSelect
}) => {
  const handleItemClick = (item: DropdownItem) => {
    if (item.disabled) return;
    
    item.onClick?.();
    onSelect?.(item);
  };

  const getPlacementClasses = () => {
    switch (placement) {
      case 'bottom-end':
        return 'right-0 mt-2';
      case 'top-start':
        return 'left-0 bottom-full mb-2';
      case 'top-end':
        return 'right-0 bottom-full mb-2';
      default:
        return 'left-0 mt-2';
    }
  };

  return (
    <Menu as="div" className={`relative inline-block text-left ${className}`}>
      <Menu.Button
        className={`
          inline-flex items-center justify-center w-full
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        disabled={disabled}
      >
        {trigger || (
          <button className="btn btn-ghost">
            选项
            <ChevronDownIcon className="ml-2 h-4 w-4" />
          </button>
        )}
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items
          className={`
            absolute z-50 w-56 origin-top-right bg-white divide-y divide-gray-100 
            rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none
            ${getPlacementClasses()}
          `}
        >
          <div className="py-1">
            {items.map((item, index) => (
              <Fragment key={item.key}>
                {item.divider && index > 0 && (
                  <div className="border-t border-gray-100 my-1" />
                )}
                <Menu.Item disabled={item.disabled}>
                  {({ active }) => (
                    <button
                      className={`
                        group flex items-center w-full px-4 py-2 text-sm text-left
                        ${active && !item.disabled ? 'bg-gray-100 text-gray-900' : 'text-gray-700'}
                        ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                        ${item.danger ? 'text-red-600 hover:bg-red-50' : ''}
                      `}
                      onClick={() => handleItemClick(item)}
                      disabled={item.disabled}
                    >
                      {item.icon && (
                        <span className="mr-3 flex-shrink-0">
                          {item.icon}
                        </span>
                      )}
                      <span className="flex-1">{item.label}</span>
                    </button>
                  )}
                </Menu.Item>
              </Fragment>
            ))}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

// 选择下拉菜单组件
export interface SelectDropdownProps {
  options: Array<{ label: string; value: any; disabled?: boolean }>;
  value?: any;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  onChange?: (value: any) => void;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
}

export const SelectDropdown: React.FC<SelectDropdownProps> = ({
  options,
  value,
  placeholder = '请选择',
  disabled = false,
  className = '',
  onChange,
  multiple = false,
  searchable = false,
  clearable = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 处理点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 过滤选项
  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  // 获取显示文本
  const getDisplayText = () => {
    if (multiple && Array.isArray(value)) {
      if (value.length === 0) return placeholder;
      if (value.length === 1) {
        const option = options.find(opt => opt.value === value[0]);
        return option?.label || placeholder;
      }
      return `已选择 ${value.length} 项`;
    } else {
      const option = options.find(opt => opt.value === value);
      return option?.label || placeholder;
    }
  };

  // 处理选择
  const handleSelect = (optionValue: any) => {
    if (multiple) {
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(optionValue)
        ? currentValues.filter(v => v !== optionValue)
        : [...currentValues, optionValue];
      onChange?.(newValues);
    } else {
      onChange?.(optionValue);
      setIsOpen(false);
    }
  };

  // 处理清除
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange?.(multiple ? [] : undefined);
  };

  // 检查是否选中
  const isSelected = (optionValue: any) => {
    if (multiple && Array.isArray(value)) {
      return value.includes(optionValue);
    }
    return value === optionValue;
  };

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* 触发器 */}
      <button
        type="button"
        className={`
          input flex items-center justify-between w-full
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${isOpen ? 'ring-2 ring-primary-500' : ''}
        `}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
      >
        <span className={`truncate ${!value || (Array.isArray(value) && value.length === 0) ? 'text-gray-500' : ''}`}>
          {getDisplayText()}
        </span>
        
        <div className="flex items-center">
          {clearable && value && (
            <button
              type="button"
              className="mr-2 text-gray-400 hover:text-gray-600"
              onClick={handleClear}
            >
              ✕
            </button>
          )}
          <ChevronDownIcon
            className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          />
        </div>
      </button>

      {/* 下拉面板 */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {/* 搜索框 */}
          {searchable && (
            <div className="p-2 border-b border-gray-100">
              <input
                type="text"
                className="input w-full"
                placeholder="搜索选项..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          )}

          {/* 选项列表 */}
          <div className="py-1">
            {filteredOptions.length === 0 ? (
              <div className="px-4 py-2 text-sm text-gray-500 text-center">
                {searchable && searchTerm ? '未找到匹配选项' : '暂无选项'}
              </div>
            ) : (
              filteredOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  className={`
                    w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center justify-between
                    ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    ${isSelected(option.value) ? 'bg-primary-50 text-primary-700' : 'text-gray-700'}
                  `}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                  disabled={option.disabled}
                >
                  <span>{option.label}</span>
                  {isSelected(option.value) && (
                    <CheckIcon className="h-4 w-4 text-primary-600" />
                  )}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// 上下文菜单组件
export interface ContextMenuProps {
  items: DropdownItem[];
  children: React.ReactNode;
  className?: string;
  onSelect?: (item: DropdownItem) => void;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  items,
  children,
  className = '',
  onSelect
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const menuRef = useRef<HTMLDivElement>(null);

  // 处理右键点击
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setPosition({ x: e.clientX, y: e.clientY });
    setIsOpen(true);
  };

  // 处理点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // 处理项目点击
  const handleItemClick = (item: DropdownItem) => {
    if (item.disabled) return;
    
    item.onClick?.();
    onSelect?.(item);
    setIsOpen(false);
  };

  return (
    <>
      <div onContextMenu={handleContextMenu} className={className}>
        {children}
      </div>

      {isOpen && (
        <div
          ref={menuRef}
          className="fixed z-50 w-48 bg-white divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
          style={{
            left: position.x,
            top: position.y,
          }}
        >
          <div className="py-1">
            {items.map((item, index) => (
              <Fragment key={item.key}>
                {item.divider && index > 0 && (
                  <div className="border-t border-gray-100 my-1" />
                )}
                <button
                  className={`
                    group flex items-center w-full px-4 py-2 text-sm text-left
                    hover:bg-gray-100 text-gray-700
                    ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    ${item.danger ? 'text-red-600 hover:bg-red-50' : ''}
                  `}
                  onClick={() => handleItemClick(item)}
                  disabled={item.disabled}
                >
                  {item.icon && (
                    <span className="mr-3 flex-shrink-0">
                      {item.icon}
                    </span>
                  )}
                  <span className="flex-1">{item.label}</span>
                </button>
              </Fragment>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default Dropdown;
