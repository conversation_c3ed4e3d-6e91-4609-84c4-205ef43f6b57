import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';
import { useGlobalSearch } from '@/hooks/useSearch';
import searchService from '@/services/searchService';

interface GlobalSearchProps {
  className?: string;
  placeholder?: string;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({
  className = '',
  placeholder = '搜索产品、分析、任务...'
}) => {
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<string[]>([]);

  const {
    results,
    query,
    isLoading,
    search,
    setQuery
  } = useGlobalSearch();

  // 加载搜索历史和热门搜索
  useEffect(() => {
    const loadSearchData = async () => {
      try {
        const [history, popular] = await Promise.all([
          searchService.getSearchHistory(10),
          searchService.getPopularSearches(5)
        ]);
        
        setSearchHistory(history.map(h => h.query));
        setPopularSearches(popular.map(p => p.query));
      } catch (error) {
        console.error('加载搜索数据失败:', error);
      }
    };

    loadSearchData();
  }, []);

  // 处理搜索
  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      search(searchQuery.trim());
      setIsFocused(false);
      setShowHistory(false);
      
      // 跳转到搜索页面
      router.push(`/search?query=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    if (value.length > 1) {
      search(value);
      setShowHistory(false);
    } else {
      setShowHistory(true);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(query);
    } else if (e.key === 'Escape') {
      setIsFocused(false);
      setShowHistory(false);
      inputRef.current?.blur();
    }
  };

  // 处理焦点
  const handleFocus = () => {
    setIsFocused(true);
    if (!query || query.length <= 1) {
      setShowHistory(true);
    }
  };

  // 处理失去焦点
  const handleBlur = () => {
    // 延迟隐藏，以便点击结果项
    setTimeout(() => {
      setIsFocused(false);
      setShowHistory(false);
    }, 200);
  };

  // 清空搜索
  const handleClear = () => {
    setQuery('');
    setShowHistory(true);
    inputRef.current?.focus();
  };

  // 选择搜索建议
  const handleSelectSuggestion = (suggestion: string) => {
    setQuery(suggestion);
    handleSearch(suggestion);
  };

  // 跳转到结果详情
  const handleResultClick = (result: any) => {
    if (result.type === 'product') {
      router.push(`/products/${result.id}`);
    } else if (result.type === 'analysis') {
      router.push(`/analysis/${result.id}`);
    } else if (result.type === 'import_task') {
      router.push(`/import/tasks/${result.id}`);
    }
    
    setIsFocused(false);
    setShowHistory(false);
  };

  const hasResults = results && (
    results.products.length > 0 || 
    results.analyses.length > 0 || 
    results.importTasks.length > 0
  );

  return (
    <div className={`global-search ${className}`} style={{ position: 'relative' }}>
      {/* 搜索输入框 */}
      <div style={{
        position: 'relative',
        width: '100%',
        maxWidth: '400px'
      }}>
        <MagnifyingGlassIcon style={{
          position: 'absolute',
          left: '0.75rem',
          top: '50%',
          transform: 'translateY(-50%)',
          width: '1.25rem',
          height: '1.25rem',
          color: 'var(--color-gray-400)',
          zIndex: 1
        }} />
        
        <input
          ref={inputRef}
          type="text"
          className="input"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          style={{
            paddingLeft: '2.5rem',
            paddingRight: query ? '2.5rem' : '0.75rem',
            width: '100%'
          }}
        />
        
        {query && (
          <button
            onClick={handleClear}
            style={{
              position: 'absolute',
              right: '0.75rem',
              top: '50%',
              transform: 'translateY(-50%)',
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: 'var(--color-gray-400)',
              zIndex: 1
            }}
          >
            <XMarkIcon style={{ width: '1.25rem', height: '1.25rem' }} />
          </button>
        )}
      </div>

      {/* 搜索结果下拉框 */}
      {isFocused && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: 0,
          right: 0,
          background: 'white',
          border: '1px solid var(--color-gray-200)',
          borderRadius: 'var(--radius-lg)',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
          zIndex: 1000,
          maxHeight: '400px',
          overflowY: 'auto',
          marginTop: '0.5rem'
        }}>
          {isLoading ? (
            <div style={{
              padding: '1rem',
              textAlign: 'center',
              color: 'var(--color-gray-600)'
            }}>
              <div className="loading-spinner" style={{ width: '1.5rem', height: '1.5rem', margin: '0 auto' }}></div>
              <p style={{ marginTop: '0.5rem', fontSize: '0.875rem' }}>搜索中...</p>
            </div>
          ) : showHistory ? (
            <div style={{ padding: '1rem' }}>
              {/* 搜索历史 */}
              {searchHistory.length > 0 && (
                <div style={{ marginBottom: '1rem' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '0.5rem'
                  }}>
                    <ClockIcon style={{ width: '1rem', height: '1rem', color: 'var(--color-gray-400)' }} />
                    <span style={{
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: 'var(--color-gray-600)',
                      textTransform: 'uppercase'
                    }}>
                      最近搜索
                    </span>
                  </div>
                  {searchHistory.slice(0, 5).map((historyItem, index) => (
                    <button
                      key={index}
                      onClick={() => handleSelectSuggestion(historyItem)}
                      style={{
                        display: 'block',
                        width: '100%',
                        padding: '0.5rem',
                        textAlign: 'left',
                        background: 'none',
                        border: 'none',
                        borderRadius: 'var(--radius-md)',
                        cursor: 'pointer',
                        fontSize: '0.875rem',
                        color: 'var(--color-gray-700)',
                        transition: 'background-color var(--transition-fast)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--color-gray-50)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      {historyItem}
                    </button>
                  ))}
                </div>
              )}

              {/* 热门搜索 */}
              {popularSearches.length > 0 && (
                <div>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '0.5rem'
                  }}>
                    <TrendingUpIcon style={{ width: '1rem', height: '1rem', color: 'var(--color-gray-400)' }} />
                    <span style={{
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: 'var(--color-gray-600)',
                      textTransform: 'uppercase'
                    }}>
                      热门搜索
                    </span>
                  </div>
                  {popularSearches.map((popularItem, index) => (
                    <button
                      key={index}
                      onClick={() => handleSelectSuggestion(popularItem)}
                      style={{
                        display: 'block',
                        width: '100%',
                        padding: '0.5rem',
                        textAlign: 'left',
                        background: 'none',
                        border: 'none',
                        borderRadius: 'var(--radius-md)',
                        cursor: 'pointer',
                        fontSize: '0.875rem',
                        color: 'var(--color-gray-700)',
                        transition: 'background-color var(--transition-fast)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--color-gray-50)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      {popularItem}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ) : hasResults ? (
            <div style={{ padding: '1rem' }}>
              {/* 产品结果 */}
              {results.products.length > 0 && (
                <div style={{ marginBottom: '1rem' }}>
                  <h4 style={{
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    color: 'var(--color-gray-600)',
                    textTransform: 'uppercase',
                    marginBottom: '0.5rem'
                  }}>
                    产品 ({results.products.length})
                  </h4>
                  {results.products.slice(0, 3).map((product) => (
                    <button
                      key={product.id}
                      onClick={() => handleResultClick(product)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.75rem',
                        width: '100%',
                        padding: '0.75rem',
                        background: 'none',
                        border: 'none',
                        borderRadius: 'var(--radius-md)',
                        cursor: 'pointer',
                        textAlign: 'left',
                        transition: 'background-color var(--transition-fast)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--color-gray-50)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      {product.imageUrl && (
                        <div style={{
                          width: '40px',
                          height: '40px',
                          background: `url(${product.imageUrl}) center/cover`,
                          borderRadius: 'var(--radius-md)',
                          flexShrink: 0
                        }} />
                      )}
                      <div style={{ flex: 1, minWidth: 0 }}>
                        <div style={{
                          fontSize: '0.875rem',
                          fontWeight: '500',
                          color: 'var(--color-gray-900)',
                          marginBottom: '0.25rem',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {product.title}
                        </div>
                        <div style={{
                          fontSize: '0.75rem',
                          color: 'var(--color-gray-600)'
                        }}>
                          {product.price && `¥${product.price.toLocaleString()}`}
                          {product.brand && ` · ${product.brand}`}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {/* 分析结果 */}
              {results.analyses.length > 0 && (
                <div style={{ marginBottom: '1rem' }}>
                  <h4 style={{
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    color: 'var(--color-gray-600)',
                    textTransform: 'uppercase',
                    marginBottom: '0.5rem'
                  }}>
                    分析 ({results.analyses.length})
                  </h4>
                  {results.analyses.slice(0, 2).map((analysis) => (
                    <button
                      key={analysis.id}
                      onClick={() => handleResultClick(analysis)}
                      style={{
                        display: 'block',
                        width: '100%',
                        padding: '0.75rem',
                        background: 'none',
                        border: 'none',
                        borderRadius: 'var(--radius-md)',
                        cursor: 'pointer',
                        textAlign: 'left',
                        transition: 'background-color var(--transition-fast)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--color-gray-50)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <div style={{
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: 'var(--color-gray-900)',
                        marginBottom: '0.25rem'
                      }}>
                        {analysis.title}
                      </div>
                      <div style={{
                        fontSize: '0.75rem',
                        color: 'var(--color-gray-600)'
                      }}>
                        分析任务 · {analysis.category}
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {/* 查看全部结果 */}
              <button
                onClick={() => handleSearch(query)}
                style={{
                  display: 'block',
                  width: '100%',
                  padding: '0.75rem',
                  background: 'var(--color-primary-50)',
                  border: '1px solid var(--color-primary-200)',
                  borderRadius: 'var(--radius-md)',
                  cursor: 'pointer',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--color-primary-700)',
                  textAlign: 'center',
                  transition: 'all var(--transition-fast)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--color-primary-100)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--color-primary-50)';
                }}
              >
                查看全部结果 ({results.total})
              </button>
            </div>
          ) : query && query.length > 1 ? (
            <div style={{
              padding: '1rem',
              textAlign: 'center',
              color: 'var(--color-gray-500)'
            }}>
              <p style={{ fontSize: '0.875rem' }}>
                未找到相关结果
              </p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default GlobalSearch;
