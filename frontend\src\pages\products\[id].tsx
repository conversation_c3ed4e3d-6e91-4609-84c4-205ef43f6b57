import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  ShoppingCartIcon,
  HeartIcon,
  ShareIcon,
  ChartBarIcon,
  StarIcon,
  TagIcon,
  GlobeAltIcon,
  CurrencyYenIcon,
  TruckIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { useProducts } from '@/hooks/useProducts';
import { useSimilarProducts } from '@/hooks/useSearch';
import { useProductAnalysis } from '@/hooks/useAnalysis';
import toast from 'react-hot-toast';

const ProductDetailPage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useRequireAuth();
  const router = useRouter();
  const { id } = router.query;
  const productId = typeof id === 'string' ? id : '';

  const [isTracking, setIsTracking] = useState(false);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [analysisDepth, setAnalysisDepth] = useState<'basic' | 'detailed' | 'competitive'>('detailed');

  // 获取产品详情
  const {
    products,
    getProduct,
    updateProduct,
    isLoading,
    isUpdating
  } = useProducts();

  const product = products.find(p => p.id === productId);

  // 获取相似产品
  const { 
    similarProducts, 
    isLoading: isLoadingSimilar 
  } = useSimilarProducts(productId);

  // 产品分析
  const { 
    analyzeProduct, 
    isAnalyzing 
  } = useProductAnalysis();

  if (authLoading || isLoading) {
    return <LoadingScreen message="加载产品详情..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  if (!product) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div className="card" style={{ maxWidth: '400px', textAlign: 'center' }}>
          <div className="card-body">
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>
              产品不存在
            </h1>
            <p style={{ color: 'var(--color-gray-600)', marginBottom: '2rem' }}>
              找不到指定的产品
            </p>
            <Link href="/products" className="btn btn-primary">
              返回产品列表
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // 处理跟踪状态切换
  const handleToggleTracking = async () => {
    try {
      setIsTracking(true);
      await updateProduct(productId, { 
        ...product, 
        isTracking: !product.isTracking 
      });
      toast.success(product.isTracking ? '已停止跟踪产品' : '已开始跟踪产品');
    } catch (error) {
      toast.error('操作失败，请重试');
    } finally {
      setIsTracking(false);
    }
  };

  // 处理分析
  const handleAnalyze = async () => {
    try {
      const result = await analyzeProduct(product, {
        analysisDepth,
        includeMarketAnalysis: true,
        includeCompetitorAnalysis: true,
        includeTrendAnalysis: true
      });
      
      if (result) {
        setShowAnalysisModal(false);
        toast.success('分析任务已创建');
        router.push(`/analysis/${result.id}`);
      }
    } catch (error) {
      toast.error('创建分析任务失败');
    }
  };

  // 处理分享
  const handleShare = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url);
    toast.success('链接已复制到剪贴板');
  };

  // 访问原始链接
  const handleVisitOriginal = () => {
    if (product.url) {
      window.open(product.url, '_blank');
    }
  };

  // 格式化价格
  const formatPrice = (price?: number) => {
    if (price === undefined) return '暂无价格';
    return `¥${price.toLocaleString()}`;
  };

  // 计算折扣
  const calculateDiscount = (current?: number, original?: number) => {
    if (!current || !original || original <= current) return null;
    return Math.round((1 - current / original) * 100);
  };

  const discount = calculateDiscount(product.currentPrice, product.originalPrice);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 返回按钮 */}
        <div style={{ marginBottom: '2rem' }}>
          <Link
            href="/products"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              color: 'var(--color-primary-600)',
              textDecoration: 'none',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
          >
            <ArrowLeftIcon style={{ width: '1rem', height: '1rem' }} />
            返回产品列表
          </Link>
        </div>

        {/* 产品详情 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 2fr',
              gap: '2rem'
            }}>
              {/* 产品图片 */}
              <div>
                <div style={{
                  width: '100%',
                  paddingBottom: '100%',
                  position: 'relative',
                  borderRadius: 'var(--radius-lg)',
                  overflow: 'hidden',
                  background: 'var(--color-gray-100)',
                  marginBottom: '1rem'
                }}>
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.title}
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  ) : (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'var(--color-gray-400)'
                    }}>
                      暂无图片
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '0.5rem'
                }}>
                  <button
                    className="btn btn-ghost"
                    onClick={handleVisitOriginal}
                    disabled={!product.url}
                  >
                    <GlobeAltIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    访问原始链接
                  </button>
                  <button
                    className="btn btn-ghost"
                    onClick={handleShare}
                  >
                    <ShareIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    分享
                  </button>
                  <button
                    className={`btn ${product.isTracking ? 'btn-primary' : 'btn-ghost'}`}
                    onClick={handleToggleTracking}
                    disabled={isTracking || isUpdating}
                  >
                    <HeartIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    {product.isTracking ? '已跟踪' : '跟踪'}
                  </button>
                </div>
              </div>

              {/* 产品信息 */}
              <div>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  marginBottom: '1rem'
                }}>
                  <div>
                    <h1 style={{
                      fontSize: '2rem',
                      fontWeight: '700',
                      color: 'var(--color-gray-900)',
                      marginBottom: '0.5rem'
                    }}>
                      {product.title}
                    </h1>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1rem',
                      fontSize: '0.875rem',
                      color: 'var(--color-gray-600)',
                      marginBottom: '1rem'
                    }}>
                      {product.brand && (
                        <span>品牌: {product.brand}</span>
                      )}
                      {product.category && (
                        <span>分类: {product.category}</span>
                      )}
                      {product.platform && (
                        <span>平台: {product.platform}</span>
                      )}
                    </div>
                  </div>
                  
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowAnalysisModal(true)}
                  >
                    <ChartBarIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    分析产品
                  </button>
                </div>

                {/* 价格信息 */}
                <div style={{
                  background: 'var(--color-gray-50)',
                  padding: '1.5rem',
                  borderRadius: 'var(--radius-lg)',
                  marginBottom: '2rem'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'baseline',
                    gap: '1rem',
                    marginBottom: '0.5rem'
                  }}>
                    <span style={{
                      fontSize: '2rem',
                      fontWeight: '700',
                      color: 'var(--color-primary-600)'
                    }}>
                      {formatPrice(product.currentPrice)}
                    </span>
                    
                    {product.originalPrice && product.originalPrice > product.currentPrice && (
                      <span style={{
                        fontSize: '1.125rem',
                        textDecoration: 'line-through',
                        color: 'var(--color-gray-500)'
                      }}>
                        {formatPrice(product.originalPrice)}
                      </span>
                    )}
                    
                    {discount && (
                      <span className="badge badge-error">
                        {discount}% 折扣
                      </span>
                    )}
                  </div>
                  
                  <div style={{
                    display: 'flex',
                    gap: '1rem',
                    flexWrap: 'wrap'
                  }}>
                    {product.stock !== undefined && (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        fontSize: '0.875rem',
                        color: 'var(--color-gray-700)'
                      }}>
                        <TruckIcon style={{ width: '1rem', height: '1rem' }} />
                        库存: {product.stock}
                      </div>
                    )}
                    
                    {product.sales !== undefined && (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        fontSize: '0.875rem',
                        color: 'var(--color-gray-700)'
                      }}>
                        <ShoppingCartIcon style={{ width: '1rem', height: '1rem' }} />
                        销量: {product.sales}
                      </div>
                    )}
                    
                    {product.rating !== undefined && (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        fontSize: '0.875rem',
                        color: 'var(--color-gray-700)'
                      }}>
                        <StarIcon style={{ width: '1rem', height: '1rem', color: '#fbbf24' }} />
                        评分: {product.rating.toFixed(1)}
                      </div>
                    )}
                  </div>
                </div>

                {/* 产品描述 */}
                {product.description && (
                  <div style={{ marginBottom: '2rem' }}>
                    <h3 style={{
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      marginBottom: '1rem'
                    }}>
                      产品描述
                    </h3>
                    <p style={{
                      fontSize: '0.875rem',
                      lineHeight: '1.6',
                      color: 'var(--color-gray-700)'
                    }}>
                      {product.description}
                    </p>
                  </div>
                )}

                {/* 标签 */}
                {product.tags && product.tags.length > 0 && (
                  <div>
                    <h3 style={{
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      marginBottom: '1rem'
                    }}>
                      标签
                    </h3>
                    <div style={{
                      display: 'flex',
                      gap: '0.5rem',
                      flexWrap: 'wrap'
                    }}>
                      {product.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="badge badge-secondary"
                          style={{ padding: '0.5rem 0.75rem' }}
                        >
                          <TagIcon style={{ width: '0.875rem', height: '0.875rem', marginRight: '0.25rem' }} />
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 相似产品 */}
        {similarProducts.length > 0 && (
          <div className="card">
            <div className="card-header">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600'
              }}>
                相似产品
              </h2>
            </div>
            <div className="card-body">
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
                gap: '1.5rem'
              }}>
                {similarProducts.map((similar) => (
                  <div
                    key={similar.id}
                    className="card"
                    style={{
                      border: '1px solid var(--color-gray-200)',
                      background: 'var(--color-gray-50)',
                      cursor: 'pointer',
                      transition: 'all var(--transition-fast)'
                    }}
                    onClick={() => router.push(`/products/${similar.id}`)}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <div className="card-body">
                      {similar.imageUrl && (
                        <div style={{
                          width: '100%',
                          height: '150px',
                          background: `url(${similar.imageUrl}) center/cover`,
                          borderRadius: 'var(--radius-md)',
                          marginBottom: '1rem'
                        }} />
                      )}
                      <h3 style={{
                        fontSize: '1rem',
                        fontWeight: '600',
                        marginBottom: '0.5rem',
                        color: 'var(--color-gray-900)'
                      }}>
                        {similar.title}
                      </h3>
                      {similar.price && (
                        <div style={{
                          fontSize: '1.125rem',
                          fontWeight: '700',
                          color: 'var(--color-primary-600)'
                        }}>
                          {formatPrice(similar.price)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 分析模态框 */}
        {showAnalysisModal && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div className="card" style={{ width: '100%', maxWidth: '500px' }}>
              <div className="card-header">
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <h2 style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    margin: 0
                  }}>
                    创建产品分析
                  </h2>
                  <button
                    className="btn btn-ghost btn-sm"
                    onClick={() => setShowAnalysisModal(false)}
                  >
                    ✕
                  </button>
                </div>
              </div>
              <div className="card-body">
                <p style={{
                  fontSize: '0.875rem',
                  color: 'var(--color-gray-600)',
                  marginBottom: '1.5rem'
                }}>
                  AI将分析该产品的市场表现、竞争情况和发展趋势，为您提供全面的洞察和建议。
                </p>
                
                <div style={{ marginBottom: '1.5rem' }}>
                  <label style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem'
                  }}>
                    分析深度
                  </label>
                  <select
                    className="input"
                    value={analysisDepth}
                    onChange={(e) => setAnalysisDepth(e.target.value as any)}
                  >
                    <option value="basic">基础分析 (约1分钟)</option>
                    <option value="detailed">详细分析 (约3分钟)</option>
                    <option value="competitive">竞争性分析 (约5分钟)</option>
                  </select>
                </div>
                
                <div style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '1rem',
                  marginTop: '2rem'
                }}>
                  <button
                    className="btn btn-ghost"
                    onClick={() => setShowAnalysisModal(false)}
                  >
                    取消
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleAnalyze}
                    disabled={isAnalyzing}
                  >
                    {isAnalyzing ? (
                      <>
                        <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                        创建中...
                      </>
                    ) : (
                      '创建分析'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetailPage;
