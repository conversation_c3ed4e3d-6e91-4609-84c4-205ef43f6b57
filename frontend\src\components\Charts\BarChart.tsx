import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ChartData
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export interface BarChartProps {
  data: ChartData<'bar'>;
  options?: ChartOptions<'bar'>;
  width?: number;
  height?: number;
  className?: string;
  title?: string;
  loading?: boolean;
  error?: string;
  horizontal?: boolean;
}

const defaultOptions: ChartOptions<'bar'> = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    tooltip: {
      mode: 'index',
      intersect: false,
    },
  },
  scales: {
    x: {
      display: true,
      grid: {
        display: true,
        color: 'rgba(0, 0, 0, 0.1)',
      },
    },
    y: {
      display: true,
      grid: {
        display: true,
        color: 'rgba(0, 0, 0, 0.1)',
      },
      beginAtZero: true,
    },
  },
};

const BarChart: React.FC<BarChartProps> = ({
  data,
  options,
  width,
  height = 400,
  className = '',
  title,
  loading = false,
  error,
  horizontal = false
}) => {
  const mergedOptions: ChartOptions<'bar'> = {
    ...defaultOptions,
    ...options,
    indexAxis: horizontal ? 'y' : 'x',
    plugins: {
      ...defaultOptions.plugins,
      ...options?.plugins,
    },
  };

  if (loading) {
    return (
      <div className={`bar-chart-container ${className}`} style={{ height }}>
        <div className="flex items-center justify-center h-full">
          <div className="loading-spinner w-8 h-8"></div>
          <span className="ml-2 text-gray-600">加载图表数据...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bar-chart-container ${className}`} style={{ height }}>
        <div className="flex items-center justify-center h-full text-red-600">
          <span>加载图表失败: {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bar-chart-container ${className}`}>
      {title && (
        <h3 className="text-lg font-semibold mb-4 text-center">{title}</h3>
      )}
      <div style={{ height }}>
        <Bar data={data} options={mergedOptions} />
      </div>
    </div>
  );
};

export default BarChart;
