import React from 'react';
import { useForm, UseFormReturn, FieldValues, SubmitHandler } from 'react-hook-form';
import { ExclamationCircleIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

// 表单字段类型定义
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'date' | 'datetime-local';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: Array<{ label: string; value: string | number }>;
  validation?: {
    required?: string;
    pattern?: { value: RegExp; message: string };
    minLength?: { value: number; message: string };
    maxLength?: { value: number; message: string };
    min?: { value: number; message: string };
    max?: { value: number; message: string };
    validate?: (value: any) => string | boolean;
  };
  className?: string;
  rows?: number; // for textarea
  multiple?: boolean; // for select and file
  accept?: string; // for file input
  step?: string; // for number input
  description?: string;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
}

export interface FormProps<T extends FieldValues = FieldValues> {
  fields: FormField[];
  onSubmit: SubmitHandler<T>;
  defaultValues?: Partial<T>;
  className?: string;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
  loading?: boolean;
  layout?: 'vertical' | 'horizontal' | 'inline';
  size?: 'small' | 'middle' | 'large';
  showSubmitButton?: boolean;
  showCancelButton?: boolean;
  submitButtonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
  cancelButtonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
  form?: UseFormReturn<T>;
}

const Form = <T extends FieldValues = FieldValues>({
  fields,
  onSubmit,
  defaultValues,
  className = '',
  submitText = '提交',
  cancelText = '取消',
  onCancel,
  loading = false,
  layout = 'vertical',
  size = 'middle',
  showSubmitButton = true,
  showCancelButton = false,
  submitButtonProps,
  cancelButtonProps,
  form: externalForm
}: FormProps<T>) => {
  const internalForm = useForm<T>({
    defaultValues: defaultValues as any
  });
  
  const form = externalForm || internalForm;
  const { register, handleSubmit, formState: { errors }, watch, setValue } = form;

  const [showPasswords, setShowPasswords] = React.useState<Record<string, boolean>>({});

  const togglePasswordVisibility = (fieldName: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  const sizeClasses = {
    small: 'text-sm',
    middle: 'text-base',
    large: 'text-lg'
  };

  const inputSizeClasses = {
    small: 'px-2 py-1 text-sm',
    middle: 'px-3 py-2 text-sm',
    large: 'px-4 py-3 text-base'
  };

  const renderField = (field: FormField) => {
    const error = errors[field.name];
    const hasError = !!error;
    
    const baseInputClass = `
      input
      ${inputSizeClasses[size]}
      ${hasError ? 'input-error' : ''}
      ${field.className || ''}
    `;

    const fieldId = `field-${field.name}`;

    // 构建验证规则
    const validationRules: any = {};
    if (field.required) {
      validationRules.required = field.validation?.required || `${field.label}是必填项`;
    }
    if (field.validation?.pattern) {
      validationRules.pattern = field.validation.pattern;
    }
    if (field.validation?.minLength) {
      validationRules.minLength = field.validation.minLength;
    }
    if (field.validation?.maxLength) {
      validationRules.maxLength = field.validation.maxLength;
    }
    if (field.validation?.min) {
      validationRules.min = field.validation.min;
    }
    if (field.validation?.max) {
      validationRules.max = field.validation.max;
    }
    if (field.validation?.validate) {
      validationRules.validate = field.validation.validate;
    }

    const renderInput = () => {
      switch (field.type) {
        case 'textarea':
          return (
            <textarea
              id={fieldId}
              className={baseInputClass}
              placeholder={field.placeholder}
              disabled={field.disabled || loading}
              rows={field.rows || 3}
              {...register(field.name as any, validationRules)}
            />
          );

        case 'select':
          return (
            <select
              id={fieldId}
              className={baseInputClass}
              disabled={field.disabled || loading}
              multiple={field.multiple}
              {...register(field.name as any, validationRules)}
            >
              {!field.required && !field.multiple && (
                <option value="">请选择{field.label}</option>
              )}
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          );

        case 'checkbox':
          return (
            <div className="flex items-center">
              <input
                id={fieldId}
                type="checkbox"
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                disabled={field.disabled || loading}
                {...register(field.name as any, validationRules)}
              />
              <label htmlFor={fieldId} className="ml-2 text-sm text-gray-700">
                {field.label}
              </label>
            </div>
          );

        case 'radio':
          return (
            <div className="space-y-2">
              {field.options?.map((option) => (
                <div key={option.value} className="flex items-center">
                  <input
                    id={`${fieldId}-${option.value}`}
                    type="radio"
                    value={option.value}
                    className="border-gray-300 text-primary-600 focus:ring-primary-500"
                    disabled={field.disabled || loading}
                    {...register(field.name as any, validationRules)}
                  />
                  <label htmlFor={`${fieldId}-${option.value}`} className="ml-2 text-sm text-gray-700">
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
          );

        case 'file':
          return (
            <input
              id={fieldId}
              type="file"
              className={baseInputClass}
              disabled={field.disabled || loading}
              multiple={field.multiple}
              accept={field.accept}
              {...register(field.name as any, validationRules)}
            />
          );

        case 'password':
          return (
            <div className="relative">
              <input
                id={fieldId}
                type={showPasswords[field.name] ? 'text' : 'password'}
                className={`${baseInputClass} pr-10`}
                placeholder={field.placeholder}
                disabled={field.disabled || loading}
                {...register(field.name as any, validationRules)}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => togglePasswordVisibility(field.name)}
              >
                {showPasswords[field.name] ? (
                  <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                ) : (
                  <EyeIcon className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
          );

        default:
          return (
            <div className="relative">
              {field.prefix && (
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  {field.prefix}
                </div>
              )}
              <input
                id={fieldId}
                type={field.type}
                className={`
                  ${baseInputClass}
                  ${field.prefix ? 'pl-10' : ''}
                  ${field.suffix ? 'pr-10' : ''}
                `}
                placeholder={field.placeholder}
                disabled={field.disabled || loading}
                step={field.step}
                {...register(field.name as any, validationRules)}
              />
              {field.suffix && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  {field.suffix}
                </div>
              )}
            </div>
          );
      }
    };

    if (field.type === 'checkbox') {
      return (
        <div key={field.name} className="form-field">
          {renderInput()}
          {hasError && (
            <div className="mt-1 flex items-center text-sm text-red-600">
              <ExclamationCircleIcon className="h-4 w-4 mr-1" />
              {error?.message}
            </div>
          )}
          {field.description && !hasError && (
            <p className="mt-1 text-sm text-gray-500">{field.description}</p>
          )}
        </div>
      );
    }

    return (
      <div key={field.name} className="form-field">
        <label htmlFor={fieldId} className="block text-sm font-medium text-gray-700 mb-1">
          {field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        {renderInput()}
        {hasError && (
          <div className="mt-1 flex items-center text-sm text-red-600">
            <ExclamationCircleIcon className="h-4 w-4 mr-1" />
            {error?.message}
          </div>
        )}
        {field.description && !hasError && (
          <p className="mt-1 text-sm text-gray-500">{field.description}</p>
        )}
      </div>
    );
  };

  const layoutClasses = {
    vertical: 'space-y-4',
    horizontal: 'grid grid-cols-2 gap-4',
    inline: 'flex flex-wrap gap-4 items-end'
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={`form ${sizeClasses[size]} ${className}`}
    >
      <div className={layoutClasses[layout]}>
        {fields.map(renderField)}
      </div>

      {(showSubmitButton || showCancelButton) && (
        <div className="form-actions mt-6 flex gap-3">
          {showCancelButton && onCancel && (
            <button
              type="button"
              className="btn btn-ghost"
              onClick={onCancel}
              disabled={loading}
              {...cancelButtonProps}
            >
              {cancelText}
            </button>
          )}
          
          {showSubmitButton && (
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
              {...submitButtonProps}
            >
              {loading ? (
                <>
                  <div className="loading-spinner w-4 h-4 mr-2"></div>
                  提交中...
                </>
              ) : (
                submitText
              )}
            </button>
          )}
        </div>
      )}
    </form>
  );
};

// 表单字段组件
export const FormField: React.FC<{
  field: FormField;
  form: UseFormReturn<any>;
  size?: 'small' | 'middle' | 'large';
}> = ({ field, form, size = 'middle' }) => {
  const { register, formState: { errors } } = form;
  const error = errors[field.name];
  const hasError = !!error;

  // ... 这里可以复用上面的 renderField 逻辑
  
  return (
    <div className="form-field">
      {/* 字段渲染逻辑 */}
    </div>
  );
};

export default Form;
