import React, { useState, useMemo } from 'react';
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronUpDownIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: string;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  sortable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  className?: string;
}

export interface TableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: (total: number, range: [number, number]) => React.ReactNode;
  };
  rowKey?: string | ((record: T) => string);
  onRow?: (record: T, index: number) => {
    onClick?: (event: React.MouseEvent) => void;
    onDoubleClick?: (event: React.MouseEvent) => void;
    onContextMenu?: (event: React.MouseEvent) => void;
    className?: string;
    style?: React.CSSProperties;
  };
  rowSelection?: {
    selectedRowKeys?: string[];
    onChange?: (selectedRowKeys: string[], selectedRows: T[]) => void;
    onSelect?: (record: T, selected: boolean, selectedRows: T[]) => void;
    onSelectAll?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
    getCheckboxProps?: (record: T) => { disabled?: boolean };
  };
  expandable?: {
    expandedRowKeys?: string[];
    onExpand?: (expanded: boolean, record: T) => void;
    expandedRowRender?: (record: T, index: number) => React.ReactNode;
  };
  scroll?: {
    x?: string | number;
    y?: string | number;
  };
  size?: 'small' | 'middle' | 'large';
  bordered?: boolean;
  showHeader?: boolean;
  title?: () => React.ReactNode;
  footer?: () => React.ReactNode;
  empty?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  searchable?: boolean;
  searchPlaceholder?: string;
}

const Table = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  pagination,
  rowKey = 'id',
  onRow,
  rowSelection,
  expandable,
  scroll,
  size = 'middle',
  bordered = false,
  showHeader = true,
  title,
  footer,
  empty,
  className = '',
  style,
  searchable = false,
  searchPlaceholder = '搜索...'
}: TableProps<T>) => {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>(
    rowSelection?.selectedRowKeys || []
  );

  // 获取行的key
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey] || index.toString();
  };

  // 搜索过滤
  const filteredData = useMemo(() => {
    if (!searchable || !searchTerm) return data;
    
    return data.filter(record => {
      return columns.some(column => {
        const value = column.dataIndex ? record[column.dataIndex] : record[column.key];
        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });
  }, [data, searchTerm, columns, searchable]);

  // 排序处理
  const sortedData = useMemo(() => {
    if (!sortConfig) return filteredData;

    return [...filteredData].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [filteredData, sortConfig]);

  // 分页处理
  const paginatedData = useMemo(() => {
    if (!pagination) return sortedData;
    
    const start = (pagination.current - 1) * pagination.pageSize;
    const end = start + pagination.pageSize;
    return sortedData.slice(start, end);
  }, [sortedData, pagination]);

  // 排序处理
  const handleSort = (key: string) => {
    setSortConfig(current => {
      if (current?.key === key) {
        if (current.direction === 'asc') {
          return { key, direction: 'desc' };
        } else {
          return null; // 取消排序
        }
      }
      return { key, direction: 'asc' };
    });
  };

  // 行选择处理
  const handleRowSelect = (record: T, selected: boolean) => {
    const key = getRowKey(record, 0);
    const newSelectedKeys = selected
      ? [...selectedRowKeys, key]
      : selectedRowKeys.filter(k => k !== key);
    
    setSelectedRowKeys(newSelectedKeys);
    
    if (rowSelection?.onChange) {
      const selectedRows = data.filter(item => 
        newSelectedKeys.includes(getRowKey(item, 0))
      );
      rowSelection.onChange(newSelectedKeys, selectedRows);
    }
    
    if (rowSelection?.onSelect) {
      const selectedRows = data.filter(item => 
        newSelectedKeys.includes(getRowKey(item, 0))
      );
      rowSelection.onSelect(record, selected, selectedRows);
    }
  };

  // 全选处理
  const handleSelectAll = (selected: boolean) => {
    const allKeys = paginatedData.map((record, index) => getRowKey(record, index));
    const newSelectedKeys = selected ? allKeys : [];
    
    setSelectedRowKeys(newSelectedKeys);
    
    if (rowSelection?.onChange) {
      const selectedRows = data.filter(item => 
        newSelectedKeys.includes(getRowKey(item, 0))
      );
      rowSelection.onChange(newSelectedKeys, selectedRows);
    }
    
    if (rowSelection?.onSelectAll) {
      const selectedRows = data.filter(item => 
        newSelectedKeys.includes(getRowKey(item, 0))
      );
      rowSelection.onSelectAll(selected, selectedRows, paginatedData);
    }
  };

  // 渲染单元格内容
  const renderCell = (column: TableColumn<T>, record: T, index: number) => {
    if (column.render) {
      const value = column.dataIndex ? record[column.dataIndex] : record[column.key];
      return column.render(value, record, index);
    }
    
    const value = column.dataIndex ? record[column.dataIndex] : record[column.key];
    return value;
  };

  // 获取排序图标
  const getSortIcon = (key: string) => {
    if (sortConfig?.key !== key) {
      return <ChevronUpDownIcon className="w-4 h-4 text-gray-400" />;
    }
    
    return sortConfig.direction === 'asc' 
      ? <ChevronUpIcon className="w-4 h-4 text-primary-600" />
      : <ChevronDownIcon className="w-4 h-4 text-primary-600" />;
  };

  const sizeClasses = {
    small: 'text-xs',
    middle: 'text-sm',
    large: 'text-base'
  };

  const paddingClasses = {
    small: 'px-2 py-1',
    middle: 'px-3 py-2',
    large: 'px-4 py-3'
  };

  return (
    <div className={`table-container ${className}`} style={style}>
      {/* 标题 */}
      {title && (
        <div className="table-title mb-4">
          {title()}
        </div>
      )}

      {/* 搜索框 */}
      {searchable && (
        <div className="table-search mb-4">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              className="input pl-10"
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      )}

      {/* 表格 */}
      <div className={`overflow-auto ${scroll?.x ? 'overflow-x-auto' : ''}`}>
        <table 
          className={`
            w-full table-auto
            ${sizeClasses[size]}
            ${bordered ? 'border border-gray-200' : ''}
          `}
          style={{ minWidth: scroll?.x }}
        >
          {/* 表头 */}
          {showHeader && (
            <thead className="bg-gray-50">
              <tr>
                {/* 选择列 */}
                {rowSelection && (
                  <th className={`${paddingClasses[size]} text-left`}>
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      checked={selectedRowKeys.length === paginatedData.length && paginatedData.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                  </th>
                )}
                
                {/* 数据列 */}
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={`
                      ${paddingClasses[size]} font-medium text-gray-900
                      ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'}
                      ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''}
                      ${column.className || ''}
                    `}
                    style={{ width: column.width }}
                    onClick={column.sortable ? () => handleSort(column.key) : undefined}
                  >
                    <div className="flex items-center gap-1">
                      {column.title}
                      {column.sortable && getSortIcon(column.key)}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
          )}

          {/* 表体 */}
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0)}
                  className={`${paddingClasses[size]} text-center`}
                >
                  <div className="flex items-center justify-center py-8">
                    <div className="loading-spinner w-6 h-6 mr-2"></div>
                    加载中...
                  </div>
                </td>
              </tr>
            ) : paginatedData.length === 0 ? (
              <tr>
                <td 
                  colSpan={columns.length + (rowSelection ? 1 : 0)} 
                  className={`${paddingClasses[size]} text-center text-gray-500`}
                >
                  {empty || (
                    <div className="py-8">
                      <div className="text-gray-400 mb-2">📋</div>
                      <div>暂无数据</div>
                    </div>
                  )}
                </td>
              </tr>
            ) : (
              paginatedData.map((record, index) => {
                const key = getRowKey(record, index);
                const rowProps = onRow?.(record, index) || {};
                const isSelected = selectedRowKeys.includes(key);
                
                return (
                  <tr
                    key={key}
                    className={`
                      hover:bg-gray-50 transition-colors
                      ${isSelected ? 'bg-primary-50' : ''}
                      ${rowProps.className || ''}
                    `}
                    style={rowProps.style}
                    onClick={rowProps.onClick}
                    onDoubleClick={rowProps.onDoubleClick}
                    onContextMenu={rowProps.onContextMenu}
                  >
                    {/* 选择列 */}
                    {rowSelection && (
                      <td className={paddingClasses[size]}>
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          checked={isSelected}
                          onChange={(e) => handleRowSelect(record, e.target.checked)}
                          disabled={rowSelection.getCheckboxProps?.(record)?.disabled}
                        />
                      </td>
                    )}
                    
                    {/* 数据列 */}
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className={`
                          ${paddingClasses[size]}
                          ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'}
                          ${column.className || ''}
                        `}
                        style={{ width: column.width }}
                      >
                        {renderCell(column, record, index)}
                      </td>
                    ))}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      {pagination && pagination.total > 0 && (
        <div className="table-pagination mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            {pagination.showTotal && pagination.showTotal(
              pagination.total,
              [
                (pagination.current - 1) * pagination.pageSize + 1,
                Math.min(pagination.current * pagination.pageSize, pagination.total)
              ]
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <button
              className="btn btn-ghost btn-sm"
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
              disabled={pagination.current <= 1}
            >
              上一页
            </button>
            
            <span className="text-sm text-gray-600">
              第 {pagination.current} 页，共 {Math.ceil(pagination.total / pagination.pageSize)} 页
            </span>
            
            <button
              className="btn btn-ghost btn-sm"
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
            >
              下一页
            </button>
          </div>
        </div>
      )}

      {/* 底部 */}
      {footer && (
        <div className="table-footer mt-4">
          {footer()}
        </div>
      )}
    </div>
  );
};

export default Table;
