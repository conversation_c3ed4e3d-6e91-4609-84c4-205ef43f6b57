import { performanceMonitor } from './performance';
import { errorMonitor } from './errorHandler';
import { createWebSocketService } from '../services/websocketService';
import { notificationService } from '../services/notificationService';

// 应用配置
export interface AppConfig {
  apiBaseUrl: string;
  wsUrl: string;
  environment: 'development' | 'production' | 'test';
  version: string;
  features: {
    websocket: boolean;
    notifications: boolean;
    analytics: boolean;
    errorReporting: boolean;
    performanceMonitoring: boolean;
  };
  limits: {
    maxFileSize: number;
    maxConcurrentUploads: number;
    requestTimeout: number;
  };
}

// 默认配置
const defaultConfig: AppConfig = {
  apiBaseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1',
  wsUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001',
  environment: (process.env.NODE_ENV as any) || 'development',
  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  features: {
    websocket: true,
    notifications: true,
    analytics: process.env.NODE_ENV === 'production',
    errorReporting: true,
    performanceMonitoring: process.env.NODE_ENV === 'production',
  },
  limits: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxConcurrentUploads: 3,
    requestTimeout: 30000, // 30秒
  },
};

// 应用初始化器
class AppInitializer {
  private static instance: AppInitializer;
  private config: AppConfig;
  private initialized = false;

  static getInstance(): AppInitializer {
    if (!AppInitializer.instance) {
      AppInitializer.instance = new AppInitializer();
    }
    return AppInitializer.instance;
  }

  constructor() {
    this.config = this.loadConfig();
  }

  // 加载配置
  private loadConfig(): AppConfig {
    try {
      // 尝试从服务器加载配置
      const serverConfig = this.loadServerConfig();
      return { ...defaultConfig, ...serverConfig };
    } catch (error) {
      console.warn('Failed to load server config, using defaults:', error);
      return defaultConfig;
    }
  }

  // 从服务器加载配置
  private loadServerConfig(): Partial<AppConfig> {
    // 这里可以从服务器API加载配置
    // 目前返回空对象，使用默认配置
    return {};
  }

  // 初始化应用
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    console.log('🚀 Initializing application...');
    console.log('📋 Config:', this.config);

    try {
      // 初始化性能监控
      if (this.config.features.performanceMonitoring) {
        this.initializePerformanceMonitoring();
      }

      // 初始化错误监控
      if (this.config.features.errorReporting) {
        this.initializeErrorReporting();
      }

      // 初始化WebSocket连接
      if (this.config.features.websocket) {
        await this.initializeWebSocket();
      }

      // 初始化通知系统
      if (this.config.features.notifications) {
        this.initializeNotifications();
      }

      // 初始化分析
      if (this.config.features.analytics) {
        this.initializeAnalytics();
      }

      // 设置全局配置
      this.setupGlobalConfig();

      // 清理过期数据
      this.cleanupExpiredData();

      this.initialized = true;
      console.log('✅ Application initialized successfully');

    } catch (error) {
      console.error('❌ Application initialization failed:', error);
      throw error;
    }
  }

  // 初始化性能监控
  private initializePerformanceMonitoring(): void {
    console.log('📊 Initializing performance monitoring...');
    performanceMonitor.startMonitoring();

    // 监控关键性能指标
    this.monitorCoreWebVitals();
  }

  // 监控核心Web指标
  private monitorCoreWebVitals(): void {
    // 监控LCP (Largest Contentful Paint)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'largest-contentful-paint') {
              performanceMonitor.recordMetric('LCP', entry.startTime);
            }
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // 监控FID (First Input Delay)
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'first-input') {
              const fid = (entry as any).processingStart - entry.startTime;
              performanceMonitor.recordMetric('FID', fid);
            }
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // 监控CLS (Cumulative Layout Shift)
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          performanceMonitor.recordMetric('CLS', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });

      } catch (error) {
        console.warn('Failed to initialize Core Web Vitals monitoring:', error);
      }
    }
  }

  // 初始化错误监控
  private initializeErrorReporting(): void {
    console.log('🚨 Initializing error reporting...');
    
    // 错误监控已在errorMonitor中自动初始化
    // 这里可以添加额外的配置
    errorMonitor.addListener((error) => {
      // 发送关键错误到分析服务
      if (error.severity === 'critical' || error.severity === 'high') {
        this.reportCriticalError(error);
      }
    });
  }

  // 报告关键错误
  private reportCriticalError(error: any): void {
    // 这里可以集成第三方错误报告服务
    console.error('Critical error reported:', error);
  }

  // 初始化WebSocket
  private async initializeWebSocket(): Promise<void> {
    console.log('🔌 Initializing WebSocket connection...');
    
    try {
      const ws = createWebSocketService({
        url: this.config.wsUrl,
        debug: this.config.environment === 'development',
        reconnectInterval: 3000,
        maxReconnectAttempts: 5,
        heartbeatInterval: 30000,
      });

      await ws.connect();
      console.log('✅ WebSocket connected');

    } catch (error) {
      console.warn('⚠️ WebSocket connection failed:', error);
      // WebSocket连接失败不应该阻止应用启动
    }
  }

  // 初始化通知系统
  private initializeNotifications(): void {
    console.log('🔔 Initializing notification system...');
    
    // 通知服务已自动初始化
    // 这里可以添加额外的配置
    notificationService.updateSettings({
      enabled: true,
      sound: true,
      desktop: true,
    });
  }

  // 初始化分析
  private initializeAnalytics(): void {
    console.log('📈 Initializing analytics...');
    
    // 这里可以集成Google Analytics、百度统计等
    if (typeof window !== 'undefined') {
      // 示例：Google Analytics
      // gtag('config', 'GA_MEASUREMENT_ID');
    }
  }

  // 设置全局配置
  private setupGlobalConfig(): void {
    // 将配置挂载到全局对象
    if (typeof window !== 'undefined') {
      (window as any).__APP_CONFIG__ = this.config;
    }

    // 设置全局错误处理
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
  }

  // 清理过期数据
  private cleanupExpiredData(): void {
    try {
      // 清理localStorage中的过期数据
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('temp_') || key.startsWith('cache_')) {
          try {
            const data = JSON.parse(localStorage.getItem(key) || '{}');
            if (data.expiresAt && Date.now() > data.expiresAt) {
              localStorage.removeItem(key);
            }
          } catch (error) {
            // 忽略解析错误
          }
        }
      });

      // 清理过期通知
      notificationService.cleanupExpired();

    } catch (error) {
      console.warn('Failed to cleanup expired data:', error);
    }
  }

  // 获取配置
  getConfig(): AppConfig {
    return { ...this.config };
  }

  // 更新配置
  updateConfig(newConfig: Partial<AppConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (typeof window !== 'undefined') {
      (window as any).__APP_CONFIG__ = this.config;
    }
  }

  // 检查功能是否启用
  isFeatureEnabled(feature: keyof AppConfig['features']): boolean {
    return this.config.features[feature];
  }

  // 获取限制配置
  getLimit(limit: keyof AppConfig['limits']): number {
    return this.config.limits[limit];
  }

  // 清理资源
  private cleanup(): void {
    console.log('🧹 Cleaning up application resources...');
    
    // 停止性能监控
    if (this.config.features.performanceMonitoring) {
      performanceMonitor.stopMonitoring();
    }

    // 断开WebSocket连接
    // WebSocket服务会自动处理清理
  }

  // 检查是否已初始化
  isInitialized(): boolean {
    return this.initialized;
  }
}

// 创建全局初始化器实例
export const appInitializer = AppInitializer.getInstance();

// 初始化函数（用于在应用启动时调用）
export async function initializeApp(): Promise<void> {
  await appInitializer.initialize();
}

// 获取应用配置
export function getAppConfig(): AppConfig {
  return appInitializer.getConfig();
}

// 检查功能是否启用
export function isFeatureEnabled(feature: keyof AppConfig['features']): boolean {
  return appInitializer.isFeatureEnabled(feature);
}

// 获取限制配置
export function getLimit(limit: keyof AppConfig['limits']): number {
  return appInitializer.getLimit(limit);
}

export default AppInitializer;
