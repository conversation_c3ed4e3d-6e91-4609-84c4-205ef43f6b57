import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  StarIcon,
  HeartIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { useSearch, useSearchUrl } from '@/hooks/useSearch';
import searchService from '@/services/searchService';

const SearchPage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useRequireAuth();
  const router = useRouter();
  const { parseUrl, updateUrl } = useSearchUrl();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  // 使用搜索Hook
  const {
    results,
    pagination,
    facets,
    suggestions,
    popularSearches,
    filterOptions,
    isLoading,
    params,
    search,
    applyFilters,
    sortBy,
    goToPage,
    updateParams
  } = useSearch({
    initialParams: parseUrl(),
    autoSearch: false
  });

  // 从URL初始化搜索词
  useEffect(() => {
    const urlParams = parseUrl();
    if (urlParams.query) {
      setSearchTerm(urlParams.query);
      search(urlParams.query, urlParams);
    }
  }, []);

  if (authLoading) {
    return <LoadingScreen message="加载搜索发现..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchTerm(query);
    if (query.trim()) {
      search(query.trim());
      updateUrl({ ...params, query: query.trim(), page: 1 });
    }
  };

  // 处理过滤器变化
  const handleFilterChange = (filterType: string, value: any) => {
    const newParams = { ...params, [filterType]: value, page: 1 };
    applyFilters(newParams);
    updateUrl(newParams);
  };

  // 处理排序变化
  const handleSortChange = (sortValue: string) => {
    sortBy(sortValue as any);
    updateUrl({ ...params, sortBy: sortValue as any, page: 1 });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    goToPage(page);
    updateUrl({ ...params, page });
  };

  // 查看产品详情
  const handleViewProduct = (result: any) => {
    if (result.type === 'product') {
      router.push(`/products/${result.id}`);
    } else if (result.type === 'analysis') {
      router.push(`/analysis/${result.id}`);
    } else if (result.type === 'import_task') {
      router.push(`/import/tasks/${result.id}`);
    }
  };

  // 格式化价格
  const formatPrice = (price: number) => {
    return `¥${price.toLocaleString()}`;
  };

  // 计算折扣
  const calculateDiscount = (current: number, original: number) => {
    return Math.round((1 - current / original) * 100);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <h1 style={{
              fontSize: '2rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              搜索发现
            </h1>
            <p style={{
              fontSize: '1rem',
              color: 'var(--color-gray-600)'
            }}>
              发现优质产品，找到最佳选择
            </p>
          </div>
        </div>

        {/* 搜索栏 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              gap: '1rem',
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
              <div style={{ flex: 1, minWidth: '300px', position: 'relative' }}>
                <MagnifyingGlassIcon style={{
                  position: 'absolute',
                  left: '0.75rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '1.25rem',
                  height: '1.25rem',
                  color: 'var(--color-gray-400)'
                }} />
                <input
                  type="text"
                  className="input"
                  placeholder="搜索产品名称、品牌或型号..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch(searchTerm);
                    }
                  }}
                  style={{ paddingLeft: '2.5rem' }}
                />
              </div>
              
              <select
                className="input"
                value={params.category || 'all'}
                onChange={(e) => handleFilterChange('category', e.target.value === 'all' ? undefined : e.target.value)}
                style={{ minWidth: '120px' }}
              >
                <option value="all">全部分类</option>
                {filterOptions?.categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              
              <button 
                className="btn btn-ghost"
                onClick={() => setShowFilters(!showFilters)}
              >
                <FunnelIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                筛选
              </button>
              
              <button 
                className="btn btn-primary"
                onClick={() => handleSearch(searchTerm)}
                disabled={isLoading}
              >
                {isLoading ? '搜索中...' : '搜索'}
              </button>
            </div>
          </div>
        </div>

        {/* 筛选面板 */}
        {showFilters && (
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-body">
              <h3 style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                marginBottom: '1rem'
              }}>
                高级筛选
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem'
              }}>
                <div>
                  <label style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    display: 'block'
                  }}>
                    平台
                  </label>
                  <select
                    className="input"
                    value={params.platform || 'all'}
                    onChange={(e) => handleFilterChange('platform', e.target.value === 'all' ? undefined : e.target.value)}
                  >
                    <option value="all">全部平台</option>
                    {filterOptions?.platforms.map(platform => (
                      <option key={platform} value={platform}>
                        {platform}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    display: 'block'
                  }}>
                    价格范围
                  </label>
                  <div style={{ display: 'flex', gap: '0.5rem' }}>
                    <input
                      type="number"
                      className="input"
                      placeholder="最低价"
                      value={params.minPrice || ''}
                      onChange={(e) => handleFilterChange('minPrice', e.target.value ? parseFloat(e.target.value) : undefined)}
                    />
                    <input
                      type="number"
                      className="input"
                      placeholder="最高价"
                      value={params.maxPrice || ''}
                      onChange={(e) => handleFilterChange('maxPrice', e.target.value ? parseFloat(e.target.value) : undefined)}
                    />
                  </div>
                </div>
                
                <div>
                  <label style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    display: 'block'
                  }}>
                    排序方式
                  </label>
                  <select
                    className="input"
                    value={params.sortBy || 'relevance'}
                    onChange={(e) => handleSortChange(e.target.value)}
                  >
                    <option value="relevance">相关度</option>
                    <option value="price_asc">价格从低到高</option>
                    <option value="price_desc">价格从高到低</option>
                    <option value="rating">评分</option>
                    <option value="sales">销量</option>
                    <option value="created_at">最新</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 搜索结果头部 */}
        {(results.length > 0 || isLoading) && (
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-body">
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div>
                  <h2 style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    marginBottom: '0.25rem'
                  }}>
                    搜索结果
                  </h2>
                  {pagination && (
                    <p style={{
                      fontSize: '0.875rem',
                      color: 'var(--color-gray-600)'
                    }}>
                      找到 {pagination.total} 个相关产品
                    </p>
                  )}
                </div>
                
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-ghost'} btn-sm`}
                    onClick={() => setViewMode('grid')}
                  >
                    <Squares2X2Icon style={{ width: '1rem', height: '1rem' }} />
                  </button>
                  <button
                    className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-ghost'} btn-sm`}
                    onClick={() => setViewMode('list')}
                  >
                    <ListBulletIcon style={{ width: '1rem', height: '1rem' }} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 搜索结果 */}
        <div className="card">
          <div className="card-body">
            {isLoading ? (
              <div style={{ textAlign: 'center', padding: '3rem' }}>
                <div className="loading-spinner" style={{ width: '2rem', height: '2rem', margin: '0 auto' }}></div>
                <p style={{ marginTop: '1rem', color: 'var(--color-gray-600)' }}>
                  搜索中...
                </p>
              </div>
            ) : results.length > 0 ? (
              <>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: viewMode === 'grid' 
                    ? 'repeat(auto-fill, minmax(280px, 1fr))' 
                    : '1fr',
                  gap: '1.5rem'
                }}>
                  {results.map((result) => {
                    const formatted = searchService.formatSearchResult(result);
                    return (
                      <div key={result.id} className="card" style={{
                        border: '1px solid var(--color-gray-200)',
                        background: 'var(--color-gray-50)',
                        cursor: 'pointer',
                        transition: 'all var(--transition-fast)'
                      }}
                      onClick={() => handleViewProduct(result)}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                      >
                        <div className="card-body">
                          {viewMode === 'grid' ? (
                            <>
                              {result.imageUrl && (
                                <div style={{
                                  width: '100%',
                                  height: '200px',
                                  background: `url(${result.imageUrl}) center/cover`,
                                  borderRadius: 'var(--radius-md)',
                                  marginBottom: '1rem'
                                }} />
                              )}
                              <h3 style={{
                                fontSize: '1rem',
                                fontWeight: '600',
                                marginBottom: '0.5rem',
                                color: 'var(--color-gray-900)'
                              }}>
                                {formatted.title}
                              </h3>
                              <p style={{
                                fontSize: '0.875rem',
                                color: 'var(--color-gray-600)',
                                marginBottom: '0.5rem'
                              }}>
                                {formatted.subtitle}
                              </p>
                              {result.price && (
                                <div style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.5rem',
                                  marginBottom: '0.5rem'
                                }}>
                                  <span style={{
                                    fontSize: '1.125rem',
                                    fontWeight: '700',
                                    color: 'var(--color-primary-600)'
                                  }}>
                                    {formatPrice(result.price)}
                                  </span>
                                  {formatted.badge && (
                                    <span className={`badge badge-${formatted.badgeColor}`}>
                                      {formatted.badge}
                                    </span>
                                  )}
                                </div>
                              )}
                              {result.rating && (
                                <div style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.25rem'
                                }}>
                                  <StarIcon style={{ width: '1rem', height: '1rem', color: '#fbbf24' }} />
                                  <span style={{ fontSize: '0.875rem' }}>
                                    {result.rating.toFixed(1)}
                                  </span>
                                </div>
                              )}
                            </>
                          ) : (
                            <div style={{ display: 'flex', gap: '1rem' }}>
                              {result.imageUrl && (
                                <div style={{
                                  width: '120px',
                                  height: '120px',
                                  background: `url(${result.imageUrl}) center/cover`,
                                  borderRadius: 'var(--radius-md)',
                                  flexShrink: 0
                                }} />
                              )}
                              <div style={{ flex: 1 }}>
                                <h3 style={{
                                  fontSize: '1.125rem',
                                  fontWeight: '600',
                                  marginBottom: '0.5rem',
                                  color: 'var(--color-gray-900)'
                                }}>
                                  {formatted.title}
                                </h3>
                                <p style={{
                                  fontSize: '0.875rem',
                                  color: 'var(--color-gray-600)',
                                  marginBottom: '0.5rem'
                                }}>
                                  {formatted.subtitle}
                                </p>
                                <p style={{
                                  fontSize: '0.875rem',
                                  color: 'var(--color-gray-700)',
                                  marginBottom: '1rem'
                                }}>
                                  {formatted.description}
                                </p>
                                <div style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between'
                                }}>
                                  {result.price && (
                                    <span style={{
                                      fontSize: '1.25rem',
                                      fontWeight: '700',
                                      color: 'var(--color-primary-600)'
                                    }}>
                                      {formatPrice(result.price)}
                                    </span>
                                  )}
                                  {result.rating && (
                                    <div style={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: '0.25rem'
                                    }}>
                                      <StarIcon style={{ width: '1rem', height: '1rem', color: '#fbbf24' }} />
                                      <span style={{ fontSize: '0.875rem' }}>
                                        {result.rating.toFixed(1)}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                {/* 分页 */}
                {pagination && pagination.pages > 1 && (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginTop: '2rem',
                    paddingTop: '2rem',
                    borderTop: '1px solid var(--color-gray-200)'
                  }}>
                    <button
                      className="btn btn-ghost btn-sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      上一页
                    </button>
                    
                    <span style={{
                      fontSize: '0.875rem',
                      color: 'var(--color-gray-600)'
                    }}>
                      第 {pagination.page} 页，共 {pagination.pages} 页
                    </span>
                    
                    <button
                      className="btn btn-ghost btn-sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.pages}
                    >
                      下一页
                    </button>
                  </div>
                )}
              </>
            ) : params.query ? (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <MagnifyingGlassIcon style={{
                  width: '4rem',
                  height: '4rem',
                  margin: '0 auto 1rem',
                  color: 'var(--color-gray-300)'
                }} />
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  marginBottom: '0.5rem'
                }}>
                  未找到相关产品
                </h3>
                <p style={{ marginBottom: '1.5rem' }}>
                  尝试使用不同的关键词或调整筛选条件
                </p>
                <button 
                  className="btn btn-primary"
                  onClick={() => {
                    setSearchTerm('');
                    setShowFilters(false);
                    updateParams({ query: undefined, category: undefined, platform: undefined });
                  }}
                >
                  清除搜索
                </button>
              </div>
            ) : (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <MagnifyingGlassIcon style={{
                  width: '4rem',
                  height: '4rem',
                  margin: '0 auto 1rem',
                  color: 'var(--color-gray-300)'
                }} />
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  marginBottom: '0.5rem'
                }}>
                  开始搜索
                </h3>
                <p style={{ marginBottom: '1.5rem' }}>
                  输入关键词搜索您感兴趣的产品
                </p>
                {popularSearches.length > 0 && (
                  <div>
                    <p style={{
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      marginBottom: '0.5rem'
                    }}>
                      热门搜索：
                    </p>
                    <div style={{
                      display: 'flex',
                      gap: '0.5rem',
                      justifyContent: 'center',
                      flexWrap: 'wrap'
                    }}>
                      {popularSearches.slice(0, 5).map((search, index) => (
                        <button
                          key={index}
                          className="btn btn-ghost btn-sm"
                          onClick={() => handleSearch(search.query)}
                        >
                          {search.query}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
